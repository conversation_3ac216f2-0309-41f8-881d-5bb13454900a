"""
简单的纹理生成脚本
在没有图像编辑软件的情况下生成基础16x16纹理
"""

# 如果你有Python环境，可以运行这个脚本生成基础纹理
# 需要安装PIL库: pip install Pillow

try:
    from PIL import Image
    import os
    
    def create_texture(color, filename):
        # 创建16x16像素的图像
        img = Image.new('RGB', (16, 16), color)
        
        # 添加简单的边框效果
        pixels = img.load()
        for i in range(16):
            for j in range(16):
                # 边框
                if i == 0 or i == 15 or j == 0 or j == 15:
                    # 边框颜色(稍微亮一些)
                    r, g, b = color
                    pixels[i, j] = (min(255, r + 40), min(255, g + 40), min(255, b + 40))
                # 内部阴影
                elif i == 1 or i == 14 or j == 1 or j == 14:
                    # 阴影颜色(稍微暗一些)
                    r, g, b = color
                    pixels[i, j] = (max(0, r - 40), max(0, g - 40), max(0, b - 40))
        
        # 保存文件
        texture_dir = "src/main/resources/assets/torfpointwar/textures/block"
        os.makedirs(texture_dir, exist_ok=True)
        img.save(os.path.join(texture_dir, filename))
        print(f"已创建: {filename}")
    
    # 生成三个纹理
    create_texture((128, 128, 128), "capture_core_neutral.png")  # 灰色
    create_texture((255, 0, 0), "capture_core_red.png")         # 红色
    create_texture((0, 0, 255), "capture_core_blue.png")        # 蓝色
    
    print("所有纹理文件已生成完成！")
    print("现在可以运行: ./gradlew build")
    
except ImportError:
    print("未安装PIL库，请使用其他方法创建纹理文件")
    print("或者运行: pip install Pillow")
except Exception as e:
    print(f"生成纹理时出错: {e}")