package com.quasar.torfpointwar.auth;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.network.AuthStatusPacket;
import com.quasar.torfpointwar.network.NetworkHandler;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 服务器端授权状态管理器
 * 负责定期检查授权状态并同步给客户端
 */
@Mod.EventBusSubscriber(modid = TorfPointWar.MODID)
public class ServerAuthManager {
    
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static final ConcurrentHashMap<ServerPlayer, Boolean> playerAuthStatus = new ConcurrentHashMap<>();
    private static boolean lastAuthStatus = false;
    private static MinecraftServer currentServer = null;
    
    @SubscribeEvent
    public static void onServerStarted(ServerStartedEvent event) {
        currentServer = event.getServer();
        
        // 启动定期授权检查任务（每5秒检查一次）
        scheduler.scheduleAtFixedRate(() -> {
            try {
                checkAndSyncAuthStatus();
            } catch (Exception e) {
                TorfPointWar.LOGGER.error("授权状态检查时发生错误: ", e);
            }
        }, 1, 5, TimeUnit.SECONDS);
        
        TorfPointWar.LOGGER.info("服务器授权管理器已启动");
    }
    
    @SubscribeEvent
    public static void onPlayerJoin(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            // 玩家加入时立即发送当前授权状态
            sendAuthStatusToPlayer(player, lastAuthStatus);
            playerAuthStatus.put(player, lastAuthStatus);
            
            TorfPointWar.LOGGER.info("玩家 {} 加入，发送授权状态: {}", 
                player.getName().getString(), lastAuthStatus);
        }
    }
    
    @SubscribeEvent
    public static void onPlayerLeave(PlayerEvent.PlayerLoggedOutEvent event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            playerAuthStatus.remove(player);
        }
    }
    
    /**
     * 检查授权状态并同步给所有玩家
     */
    private static void checkAndSyncAuthStatus() {
        if (currentServer == null) return;
        
        // 检查当前授权状态
        Level overworld = currentServer.overworld();
        boolean currentAuthStatus = AuthValidator.performAuthCheck(overworld, System.currentTimeMillis());
        
        // 如果状态发生变化，通知所有玩家
        if (currentAuthStatus != lastAuthStatus) {
            TorfPointWar.LOGGER.info("授权状态变化: {} -> {}", lastAuthStatus, currentAuthStatus);
            lastAuthStatus = currentAuthStatus;
            
            // 向所有在线玩家发送新的授权状态
            for (ServerPlayer player : currentServer.getPlayerList().getPlayers()) {
                sendAuthStatusToPlayer(player, currentAuthStatus);
                playerAuthStatus.put(player, currentAuthStatus);
            }
        }
    }
    
    /**
     * 向指定玩家发送授权状态
     */
    private static void sendAuthStatusToPlayer(ServerPlayer player, boolean authorized) {
        try {
            NetworkHandler.sendToPlayer(new AuthStatusPacket(authorized), player);
        } catch (Exception e) {
            TorfPointWar.LOGGER.warn("向玩家 {} 发送授权状态时发生错误: {}", 
                player.getName().getString(), e.getMessage());
        }
    }
    
    /**
     * 获取当前授权状态
     */
    public static boolean getCurrentAuthStatus() {
        return lastAuthStatus;
    }
    
    /**
     * 强制同步授权状态给所有玩家
     */
    public static void forceSyncToAllPlayers() {
        if (currentServer == null) return;
        
        Level overworld = currentServer.overworld();
        boolean currentAuthStatus = AuthValidator.performAuthCheck(overworld, System.currentTimeMillis());
        lastAuthStatus = currentAuthStatus;
        
        for (ServerPlayer player : currentServer.getPlayerList().getPlayers()) {
            sendAuthStatusToPlayer(player, currentAuthStatus);
            playerAuthStatus.put(player, currentAuthStatus);
        }
        
        TorfPointWar.LOGGER.info("强制同步授权状态给所有玩家: {}", currentAuthStatus);
    }
}
