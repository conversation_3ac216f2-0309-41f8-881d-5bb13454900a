package com.quasar.torfpointwar.auth;

import com.quasar.torfpointwar.TorfPointWar;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class ClientAuthCache {

    private static boolean authorized = true;
    private static long lastUpdateTime = 0;

    public static void setAuthorized(boolean auth) {
        if (authorized != auth) {
            TorfPointWar.LOGGER.info("客户端授权状态更新: {}", auth);
        }
        authorized = auth;
        lastUpdateTime = System.currentTimeMillis();
    }

    public static boolean isAuthorized() {
        return authorized;
    }

    public static void reset() {
        authorized = true;
        lastUpdateTime = 0;
        TorfPointWar.LOGGER.info("客户端授权状态已重置为默认启用");
    }

    public static long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public static boolean isExpired() {
        return System.currentTimeMillis() - lastUpdateTime > 30000;
    }
}
