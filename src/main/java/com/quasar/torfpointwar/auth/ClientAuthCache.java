package com.quasar.torfpointwar.auth;

import com.quasar.torfpointwar.TorfPointWar;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

/**
 * 客户端授权状态缓存
 * 用于存储从服务器同步的授权状态
 */
@OnlyIn(Dist.CLIENT)
public class ClientAuthCache {
    
    private static boolean authorized = true; // 默认为true，允许UI显示
    private static long lastUpdateTime = 0;
    
    /**
     * 设置授权状态（由网络包调用）
     */
    public static void setAuthorized(boolean auth) {
        if (authorized != auth) {
            TorfPointWar.LOGGER.info("客户端授权状态更新: {}", auth);
        }
        authorized = auth;
        lastUpdateTime = System.currentTimeMillis();
    }
    
    /**
     * 获取当前授权状态
     */
    public static boolean isAuthorized() {
        return authorized;
    }
    
    /**
     * 重置授权状态（用于断开连接时）
     */
    public static void reset() {
        authorized = true; // 重置时也保持为true，允许UI显示
        lastUpdateTime = 0;
        TorfPointWar.LOGGER.info("客户端授权状态已重置为默认启用");
    }
    
    /**
     * 获取最后更新时间
     */
    public static long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * 检查授权状态是否过期（超过30秒未更新）
     */
    public static boolean isExpired() {
        return System.currentTimeMillis() - lastUpdateTime > 30000;
    }
}
