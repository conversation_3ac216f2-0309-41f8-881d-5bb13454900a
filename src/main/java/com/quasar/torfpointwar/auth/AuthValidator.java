package com.quasar.torfpointwar.auth;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.ServerScoreboard;
import net.minecraft.world.level.Level;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.fml.loading.FMLLoader;

public class AuthValidator {
    private static boolean hasLoggedForWorld = false;

    public static boolean isAuthorized(Level level) {

        if (level.isClientSide) {

            return DistExecutor.unsafeCallWhenOn(Dist.CLIENT, () -> () -> ClientAuthCache.isAuthorized());
        } else {

            return performAuthCheck(level, System.currentTimeMillis());
        }
    }

    public static boolean performAuthCheck(Level level, long currentTime) {
        try {

            boolean devMode = isDevelopmentMode();

            if (level == null) {
                return false;
            }

            MinecraftServer server = level.getServer();
            if (server == null) {
                return false;
            }

            ServerScoreboard scoreboard = server.getScoreboard();
            if (scoreboard == null) {
                return false;
            }

            boolean hasBZ = scoreboard.getObjective("BZ") != null;
            boolean hasKevin = scoreboard.getObjective("Kevin") != null;
            boolean authorized = hasBZ && hasKevin;

            if (server.getPlayerCount() > 0 && !hasLoggedForWorld) {
                hasLoggedForWorld = true;
                server.getPlayerList().broadcastSystemMessage(
                    net.minecraft.network.chat.Component.literal(
                        "[TorfPointWar] 授权状态: " + (devMode || authorized)
                    ), false
                );
            }

            return devMode || authorized;

        } catch (Exception e) {

            return false;
        }
    }

    private static boolean isDevelopmentMode() {

        return false;
    }

    public static boolean checkQuietly(Level level) {
        try {
            return isAuthorized(level);
        } catch (Exception e) {
            return false;
        }
    }

    public static void resetLogState() {
        hasLoggedForWorld = false;
    }
}