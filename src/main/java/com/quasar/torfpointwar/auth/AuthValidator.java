package com.quasar.torfpointwar.auth;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.ServerScoreboard;
import net.minecraft.world.level.Level;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.fml.loading.FMLLoader;

public class AuthValidator {
    private static boolean hasLoggedForWorld = false;
    
    public static boolean isAuthorized(Level level) {
        // 客户端和服务器端使用不同的授权检查逻辑
        if (level.isClientSide) {
            // 客户端：使用缓存的授权状态
            return DistExecutor.unsafeCallWhenOn(Dist.CLIENT, () -> () -> ClientAuthCache.isAuthorized());
        } else {
            // 服务器端：检查计分板
            return performAuthCheck(level, System.currentTimeMillis());
        }
    }
    
    public static boolean performAuthCheck(Level level, long currentTime) {
        try {
            // 检查开发环境
            boolean devMode = isDevelopmentMode();
            
            // 获取服务器实例
            if (level == null) {
                return false;
            }
            
            MinecraftServer server = level.getServer();
            if (server == null) {
                return false;
            }
            
            // 检查计分板
            ServerScoreboard scoreboard = server.getScoreboard();
            if (scoreboard == null) {
                return false;
            }
            
            // 检查两个特定计分板是否存在
            boolean hasBZ = scoreboard.getObjective("BZ") != null;
            boolean hasKevin = scoreboard.getObjective("Kevin") != null;
            boolean authorized = hasBZ && hasKevin;
            
            // 只在世界启动时输出一次调试信息
            if (server.getPlayerCount() > 0 && !hasLoggedForWorld) {
                hasLoggedForWorld = true;
                server.getPlayerList().broadcastSystemMessage(
                    net.minecraft.network.chat.Component.literal(
                        "[TorfPointWar] 授权状态: " + (devMode || authorized)
                    ), false
                );
            }
            
            return devMode || authorized;
            
        } catch (Exception e) {
            // 发生任何异常都返回false，确保安全
            return false;
        }
    }
    
    private static boolean isDevelopmentMode() {
        // 暂时禁用开发模式，强制使用计分板检查
        return false;
    }
    
    // 静默检查方法，不会抛出异常
    public static boolean checkQuietly(Level level) {
        try {
            return isAuthorized(level);
        } catch (Exception e) {
            return false;
        }
    }
    
    // 重置日志状态（用于新世界）
    public static void resetLogState() {
        hasLoggedForWorld = false;
    }
}