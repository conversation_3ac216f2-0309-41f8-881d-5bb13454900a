package com.quasar.torfpointwar.item;

import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.item.context.BlockPlaceContext;

public class CaptureCoreBlockItem extends BlockItem {

    public CaptureCoreBlockItem(Block block, Properties properties) {
        super(block, properties);
    }

    @Override
    public Component getName(ItemStack stack) {

        if (stack.hasCustomHoverName()) {
            return stack.getHoverName();
        }
        return super.getName(stack);
    }

    @Override
    protected boolean placeBlock(BlockPlaceContext context, net.minecraft.world.level.block.state.BlockState state) {
        boolean placed = super.placeBlock(context, state);

        if (placed && context.getItemInHand().hasCustomHoverName()) {
            Level level = context.getLevel();
            BlockPos pos = context.getClickedPos();
            BlockEntity blockEntity = level.getBlockEntity(pos);

            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                String customName = context.getItemInHand().getHoverName().getString();
                captureCore.setCoreName(customName);
            }
        }

        return placed;
    }
}