package com.quasar.torfpointwar.item;

import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class ReinforcementBanner extends Item {
    
    public enum BannerType {
        NEUTRAL("neutral", 0x808080, "中立"),
        RED("red", 0xFF0000, "红队"),
        BLUE("blue", 0x0000FF, "蓝队");
        
        private final String name;
        private final int color;
        private final String displayName;
        
        BannerType(String name, int color, String displayName) {
            this.name = name;
            this.color = color;
            this.displayName = displayName;
        }
        
        public String getName() { return name; }
        public int getColor() { return color; }
        public String getDisplayName() { return displayName; }
    }
    
    private final BannerType bannerType;
    
    public ReinforcementBanner(Properties properties, BannerType bannerType) {
        super(properties);
        this.bannerType = bannerType;
    }
    
    public BannerType getBannerType() {
        return bannerType;
    }
    
    public boolean useOnBlock(Level level, Player player, BlockPos pos, InteractionHand hand) {
        if (!level.isClientSide) {
            BlockEntity blockEntity = level.getBlockEntity(pos);
            
            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                // 尝试激活增援
                if (tryActivateReinforcement(level, player, captureCore, pos)) {
                    // 消耗旗帜
                    if (!player.getAbilities().instabuild) {
                        player.getItemInHand(hand).shrink(1);
                    }
                    return true;
                }
            } else {
                player.sendSystemMessage(Component.literal("必须对准占领核心方块使用增援旗帜！").withStyle(ChatFormatting.RED));
            }
        }
        
        return false;
    }
    
    @Override
    public InteractionResultHolder<ItemStack> use(Level level, Player player, InteractionHand hand) {
        // 这个方法现在主要用于空气中右键的处理
        if (!level.isClientSide) {
            player.sendSystemMessage(Component.literal("请对准占领核心方块使用增援旗帜！").withStyle(ChatFormatting.YELLOW));
        }
        return InteractionResultHolder.pass(player.getItemInHand(hand));
    }
    
    private boolean tryActivateReinforcement(Level level, Player player, CaptureCoreBlockEntity captureCore, BlockPos pos) {
        // 检查玩家队伍
        TeamSystem.Team playerTeam = TeamSystem.getEntityTeam(player);
        
        // 中立旗帜自动转换为玩家队伍
        BannerType effectiveBannerType = bannerType;
        if (bannerType == BannerType.NEUTRAL) {
            if (playerTeam == TeamSystem.Team.RED) {
                effectiveBannerType = BannerType.RED;
            } else if (playerTeam == TeamSystem.Team.BLUE) {
                effectiveBannerType = BannerType.BLUE;
            } else {
                player.sendSystemMessage(Component.literal("中立玩家无法使用增援旗帜！").withStyle(ChatFormatting.RED));
                return false;
            }
        }
        
        // 检查旗帜颜色与玩家队伍是否匹配
        if ((effectiveBannerType == BannerType.RED && playerTeam != TeamSystem.Team.RED) ||
            (effectiveBannerType == BannerType.BLUE && playerTeam != TeamSystem.Team.BLUE)) {
            player.sendSystemMessage(Component.literal("旗帜颜色与你的队伍不匹配！").withStyle(ChatFormatting.RED));
            return false;
        }
        
        // 检查玩家是否在据点范围内
        double distance = player.distanceToSqr(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
        double radius = captureCore.getRadius();
        if (distance > radius * radius) {
            player.sendSystemMessage(Component.literal("你不在据点范围内！").withStyle(ChatFormatting.RED));
            return false;
        }
        
        // 检查据点是否被玩家队伍控制（基于方块状态）
        BlockState blockState = level.getBlockState(pos);
        com.quasar.torfpointwar.block.CaptureCore.CaptureState captureState = blockState.getValue(com.quasar.torfpointwar.block.CaptureCore.CAPTURE_STATE);
        
        boolean canUseReinforcement = false;
        if (playerTeam == TeamSystem.Team.RED && captureState == com.quasar.torfpointwar.block.CaptureCore.CaptureState.RED_CAPTURED) {
            canUseReinforcement = true;
        } else if (playerTeam == TeamSystem.Team.BLUE && captureState == com.quasar.torfpointwar.block.CaptureCore.CaptureState.BLUE_CAPTURED) {
            canUseReinforcement = true;
        }
        
        if (!canUseReinforcement) {
            player.sendSystemMessage(Component.literal("只能在己方完全控制的据点使用增援旗帜！").withStyle(ChatFormatting.RED));
            return false;
        }
        
        // 检查据点是否已经激活增援
        if (captureCore.isReinforced()) {
            player.sendSystemMessage(Component.literal("该据点已经激活了增援效果！").withStyle(ChatFormatting.RED));
            return false;
        }
        
        // 激活增援
        captureCore.activateReinforcement();
        player.sendSystemMessage(Component.literal("已激活增援效果！据点 " + captureCore.getCoreName() + " 将在2分钟内获得双倍防守加成。").withStyle(ChatFormatting.GREEN));
        
        return true;
    }
    
    @Override
    public void appendHoverText(ItemStack stack, @Nullable Level level, List<Component> tooltip, TooltipFlag flag) {
        super.appendHoverText(stack, level, tooltip, flag);
        
        tooltip.add(Component.literal("队伍: " + bannerType.getDisplayName()).withStyle(ChatFormatting.GRAY));
        tooltip.add(Component.literal(""));
        tooltip.add(Component.literal("对准占领核心方块右键使用").withStyle(ChatFormatting.YELLOW));
        tooltip.add(Component.literal("在己方控制的据点内激活增援效果").withStyle(ChatFormatting.YELLOW));
        tooltip.add(Component.literal("增援持续2分钟，使防守兵力翻倍").withStyle(ChatFormatting.YELLOW));
        
        if (bannerType == BannerType.NEUTRAL) {
            tooltip.add(Component.literal(""));
            tooltip.add(Component.literal("中立旗帜会自动转换为使用者的队伍颜色").withStyle(ChatFormatting.AQUA));
        }
    }
    
    @Override
    public Component getName(ItemStack stack) {
        return Component.literal(bannerType.getDisplayName() + "增援旗帜");
    }
}