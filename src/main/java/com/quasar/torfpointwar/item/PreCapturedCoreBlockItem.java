package com.quasar.torfpointwar.item;

import com.quasar.torfpointwar.block.CaptureCore;
import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.core.BlockPos;
import net.minecraft.world.item.context.BlockPlaceContext;

public class PreCapturedCoreBlockItem extends BlockItem {

    private final TeamSystem.Team team;

    public PreCapturedCoreBlockItem(Block block, Properties properties, TeamSystem.Team team) {
        super(block, properties);
        this.team = team;
    }

    @Override
    public Component getName(ItemStack stack) {
        String teamName;
        if (team == TeamSystem.Team.RED) {
            teamName = "红队";
        } else if (team == TeamSystem.Team.BLUE) {
            teamName = "蓝队";
        } else {
            teamName = "未知";
        }

        if (stack.hasCustomHoverName()) {
            return Component.literal(teamName + "占领核心: ").append(stack.getHoverName());
        }
        return Component.literal(teamName + "占领核心");
    }

    @Override
    protected boolean placeBlock(BlockPlaceContext context, BlockState state) {

        if (team == TeamSystem.Team.RED) {
            state = state.setValue(CaptureCore.CAPTURE_STATE, CaptureCore.CaptureState.RED_CAPTURED);
        } else if (team == TeamSystem.Team.BLUE) {
            state = state.setValue(CaptureCore.CAPTURE_STATE, CaptureCore.CaptureState.BLUE_CAPTURED);
        }

        boolean placed = super.placeBlock(context, state);

        if (placed) {
            Level level = context.getLevel();
            BlockPos pos = context.getClickedPos();

            level.setBlock(pos, state, 3);

            BlockEntity blockEntity = level.getBlockEntity(pos);
            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {

                if (team == TeamSystem.Team.RED) {
                    captureCore.setCaptureProgress(100.0);
                } else if (team == TeamSystem.Team.BLUE) {
                    captureCore.setCaptureProgress(-100.0);
                }
                captureCore.setControllingTeam(team);

                if (context.getItemInHand().hasCustomHoverName()) {
                    String customName = context.getItemInHand().getHoverName().getString();
                    captureCore.setCoreName(customName);
                } else {
                    String teamName = team == TeamSystem.Team.RED ? "红队据点" : "蓝队据点";
                    captureCore.setCoreName(teamName);
                }
            }
        }

        return placed;
    }
}