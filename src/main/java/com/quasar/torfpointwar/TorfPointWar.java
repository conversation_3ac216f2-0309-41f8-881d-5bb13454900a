package com.quasar.torfpointwar;

import com.quasar.torfpointwar.network.NetworkHandler;
import com.quasar.torfpointwar.registry.ModBlockEntities;
import com.quasar.torfpointwar.registry.ModBlocks;
import com.quasar.torfpointwar.registry.ModItems;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Mod("torfpointwar")
public class TorfPointWar {
    public static final String MODID = "torfpointwar";
    public static final Logger LOGGER = LoggerFactory.getLogger(MODID);

    public TorfPointWar() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();

        // 注册所有组件
        ModBlocks.register(modEventBus);
        ModBlockEntities.register(modEventBus);
        ModItems.register(modEventBus);

        // 注册网络处理器
        try {
            NetworkHandler.register();
            LOGGER.info("TorfPointWar模组已加载，F3+B透视功能已禁用，网络功能已启用");
        } catch (Exception e) {
            LOGGER.warn("网络功能初始化失败，但F3+B禁用功能仍然有效: {}", e.getMessage());
        }
    }
}