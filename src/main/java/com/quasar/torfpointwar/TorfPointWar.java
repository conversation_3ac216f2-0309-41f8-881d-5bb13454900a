package com.quasar.torfpointwar;

import com.quasar.torfpointwar.network.NetworkHandler;
import com.quasar.torfpointwar.registry.ModBlockEntities;
import com.quasar.torfpointwar.registry.ModBlocks;
import com.quasar.torfpointwar.registry.ModItems;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Mod("torfpointwar")
public class TorfPointWar {
    public static final String MODID = "torfpointwar";
    public static final Logger LOGGER = LoggerFactory.getLogger(MODID);

    public TorfPointWar() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();

        ModBlocks.register(modEventBus);
        ModBlockEntities.register(modEventBus);
        ModItems.register(modEventBus);

        try {
            LOGGER.info("TorfPointWar模组已加载");
        } catch (Exception e) {
            LOGGER.warn("模组初始化失败: {}", e.getMessage());
        }
    }
}