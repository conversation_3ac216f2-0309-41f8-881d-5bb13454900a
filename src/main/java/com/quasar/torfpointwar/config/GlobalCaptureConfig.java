package com.quasar.torfpointwar.config;

import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.core.BlockPos;
import net.minecraftforge.server.ServerLifecycleHooks;

import java.util.HashSet;
import java.util.Set;

public class GlobalCaptureConfig extends SavedData {
    
    private static final String DATA_NAME = "torfpointwar_global_config";
    private static GlobalCaptureConfig instance;
    
    // 全局默认配置
    private double defaultRadius = 5.0;
    private double defaultCaptureSpeed = 1.0;
    private double defaultRevertSpeed = 0.5;
    
    // 跟踪所有据点位置，用于实时更新
    private final Set<BlockPos> allCaptureBlocks = new HashSet<>();
    
    public GlobalCaptureConfig() {
        super();
    }
    
    public static GlobalCaptureConfig getInstance() {
        if (instance == null) {
            ServerLevel overworld = ServerLifecycleHooks.getCurrentServer().overworld();
            instance = overworld.getDataStorage().computeIfAbsent(
                GlobalCaptureConfig::load, GlobalCaptureConfig::new, DATA_NAME);
        }
        return instance;
    }
    
    public static GlobalCaptureConfig load(CompoundTag tag) {
        GlobalCaptureConfig config = new GlobalCaptureConfig();
        
        if (tag.contains("DefaultRadius")) {
            config.defaultRadius = tag.getDouble("DefaultRadius");
        }
        if (tag.contains("DefaultCaptureSpeed")) {
            config.defaultCaptureSpeed = tag.getDouble("DefaultCaptureSpeed");
        }
        if (tag.contains("DefaultRevertSpeed")) {
            config.defaultRevertSpeed = tag.getDouble("DefaultRevertSpeed");
        }
        
        // 加载据点位置列表
        if (tag.contains("CaptureBlocks")) {
            CompoundTag blocksTag = tag.getCompound("CaptureBlocks");
            for (String posString : blocksTag.getAllKeys()) {
                try {
                    String[] coords = posString.split(",");
                    int x = Integer.parseInt(coords[0]);
                    int y = Integer.parseInt(coords[1]);
                    int z = Integer.parseInt(coords[2]);
                    config.allCaptureBlocks.add(new BlockPos(x, y, z));
                } catch (Exception e) {
                    // 忽略无效的位置数据
                }
            }
        }
        
        return config;
    }
    
    @Override
    public CompoundTag save(CompoundTag tag) {
        tag.putDouble("DefaultRadius", defaultRadius);
        tag.putDouble("DefaultCaptureSpeed", defaultCaptureSpeed);
        tag.putDouble("DefaultRevertSpeed", defaultRevertSpeed);
        
        // 保存据点位置列表
        CompoundTag blocksTag = new CompoundTag();
        for (BlockPos pos : allCaptureBlocks) {
            String posString = pos.getX() + "," + pos.getY() + "," + pos.getZ();
            blocksTag.putBoolean(posString, true);
        }
        tag.put("CaptureBlocks", blocksTag);
        
        return tag;
    }
    
    // Getters
    public double getDefaultRadius() { return defaultRadius; }
    public double getDefaultCaptureSpeed() { return defaultCaptureSpeed; }
    public double getDefaultRevertSpeed() { return defaultRevertSpeed; }
    
    // 全局设置方法，会实时更新所有据点
    public void setGlobalRadius(double radius) {
        this.defaultRadius = radius;
        updateAllCaptureBlocks("radius", radius);
        setDirty();
    }
    
    public void setGlobalCaptureSpeed(double speed) {
        this.defaultCaptureSpeed = speed;
        updateAllCaptureBlocks("captureSpeed", speed);
        setDirty();
    }
    
    public void setGlobalRevertSpeed(double speed) {
        this.defaultRevertSpeed = speed;
        updateAllCaptureBlocks("revertSpeed", speed);
        setDirty();
    }
    
    // 单个据点设置方法
    public void setSingleRadius(BlockPos pos, double radius) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;
        
        // 在所有维度中查找并更新该据点
        ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
            updateSingleCaptureBlockInLevel(level, pos, "radius", radius);
        });
    }
    
    public void setSingleCaptureSpeed(BlockPos pos, double speed) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;
        
        ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
            updateSingleCaptureBlockInLevel(level, pos, "captureSpeed", speed);
        });
    }
    
    public void setSingleRevertSpeed(BlockPos pos, double speed) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;
        
        ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
            updateSingleCaptureBlockInLevel(level, pos, "revertSpeed", speed);
        });
    }
    
    // 注册据点
    public void registerCaptureBlock(BlockPos pos) {
        allCaptureBlocks.add(pos);
        setDirty();
    }
    
    // 移除据点
    public void unregisterCaptureBlock(BlockPos pos) {
        allCaptureBlocks.remove(pos);
        setDirty();
    }
    
    // 实时更新所有据点的指定参数
    private void updateAllCaptureBlocks(String parameter, double value) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;
        
        // 如果注册的据点集合为空，进行全世界扫描来找到所有据点
        if (allCaptureBlocks.isEmpty()) {
            scanAndUpdateAllCaptureBlocks(parameter, value);
        } else {
            // 遍历所有维度来查找已注册的据点
            ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
                for (BlockPos pos : allCaptureBlocks) {
                    updateSingleCaptureBlockInLevel(level, pos, parameter, value);
                }
            });
        }
    }
    
    // 扫描整个世界来找到所有据点并更新（作为兜底机制）
    private void scanAndUpdateAllCaptureBlocks(String parameter, double value) {
        // 简化的实现：提供一个手动注册机制，而不是复杂的世界扫描
        // 当据点集合为空时，给出提示信息，要求重新创建据点或手动重新加载
        
        // 暂时跳过自动扫描，因为这需要访问受保护的字段
        // 用户可以通过 cleanup 命令或重新创建据点来重新注册
        System.out.println("[Torf Point War] 警告: 据点注册表为空，全局设置可能不会立即生效。");
        System.out.println("[Torf Point War] 建议使用 /tpwadmin cleanup 命令重新扫描据点，或重新创建据点。");
        
        // 标记设置已更改，下次据点初始化时会使用新的全局默认值
        setDirty();
    }
    
    // 更新单个据点的参数（在指定维度中）
    private void updateSingleCaptureBlockInLevel(ServerLevel level, BlockPos pos, String parameter, double value) {
        BlockEntity blockEntity = level.getBlockEntity(pos);
        
        if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
            switch (parameter) {
                case "radius":
                    captureCore.setRadius(value);
                    break;
                case "captureSpeed":
                    captureCore.setCaptureSpeed(value);
                    break;
                case "revertSpeed":
                    captureCore.setRevertSpeed(value);
                    break;
            }
        }
    }
    
    // 获取所有据点数量
    public int getCaptureBlockCount() {
        return allCaptureBlocks.size();
    }
    
    // 清理无效的据点位置
    public void cleanupInvalidBlocks() {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;
        
        Set<BlockPos> toRemove = new HashSet<>();
        
        for (BlockPos pos : allCaptureBlocks) {
            boolean found = false;
            // 在所有维度中检查是否还存在该据点
            for (ServerLevel level : ServerLifecycleHooks.getCurrentServer().getAllLevels()) {
                BlockEntity blockEntity = level.getBlockEntity(pos);
                if (blockEntity instanceof CaptureCoreBlockEntity) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                toRemove.add(pos);
            }
        }
        
        allCaptureBlocks.removeAll(toRemove);
        if (!toRemove.isEmpty()) {
            setDirty();
        }
    }
}