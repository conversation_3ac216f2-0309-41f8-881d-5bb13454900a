package com.quasar.torfpointwar.config;

import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.core.BlockPos;
import net.minecraftforge.server.ServerLifecycleHooks;

import java.util.HashSet;
import java.util.Set;

public class GlobalCaptureConfig extends SavedData {

    private static final String DATA_NAME = "torfpointwar_global_config";
    private static GlobalCaptureConfig instance;

    private double defaultRadius = 5.0;
    private double defaultCaptureSpeed = 1.0;
    private double defaultRevertSpeed = 0.5;

    private final Set<BlockPos> allCaptureBlocks = new HashSet<>();

    public GlobalCaptureConfig() {
        super();
    }

    public static GlobalCaptureConfig getInstance() {
        if (instance == null) {
            ServerLevel overworld = ServerLifecycleHooks.getCurrentServer().overworld();
            instance = overworld.getDataStorage().computeIfAbsent(
                GlobalCaptureConfig::load, GlobalCaptureConfig::new, DATA_NAME);
        }
        return instance;
    }

    public static GlobalCaptureConfig load(CompoundTag tag) {
        GlobalCaptureConfig config = new GlobalCaptureConfig();

        if (tag.contains("DefaultRadius")) {
            config.defaultRadius = tag.getDouble("DefaultRadius");
        }
        if (tag.contains("DefaultCaptureSpeed")) {
            config.defaultCaptureSpeed = tag.getDouble("DefaultCaptureSpeed");
        }
        if (tag.contains("DefaultRevertSpeed")) {
            config.defaultRevertSpeed = tag.getDouble("DefaultRevertSpeed");
        }

        if (tag.contains("CaptureBlocks")) {
            CompoundTag blocksTag = tag.getCompound("CaptureBlocks");
            for (String posString : blocksTag.getAllKeys()) {
                try {
                    String[] coords = posString.split(",");
                    int x = Integer.parseInt(coords[0]);
                    int y = Integer.parseInt(coords[1]);
                    int z = Integer.parseInt(coords[2]);
                    config.allCaptureBlocks.add(new BlockPos(x, y, z));
                } catch (Exception e) {

                }
            }
        }

        return config;
    }

    @Override
    public CompoundTag save(CompoundTag tag) {
        tag.putDouble("DefaultRadius", defaultRadius);
        tag.putDouble("DefaultCaptureSpeed", defaultCaptureSpeed);
        tag.putDouble("DefaultRevertSpeed", defaultRevertSpeed);

        CompoundTag blocksTag = new CompoundTag();
        for (BlockPos pos : allCaptureBlocks) {
            String posString = pos.getX() + "," + pos.getY() + "," + pos.getZ();
            blocksTag.putBoolean(posString, true);
        }
        tag.put("CaptureBlocks", blocksTag);

        return tag;
    }

    public double getDefaultRadius() { return defaultRadius; }
    public double getDefaultCaptureSpeed() { return defaultCaptureSpeed; }
    public double getDefaultRevertSpeed() { return defaultRevertSpeed; }

    public void setGlobalRadius(double radius) {
        this.defaultRadius = radius;
        updateAllCaptureBlocks("radius", radius);
        setDirty();
    }

    public void setGlobalCaptureSpeed(double speed) {
        this.defaultCaptureSpeed = speed;
        updateAllCaptureBlocks("captureSpeed", speed);
        setDirty();
    }

    public void setGlobalRevertSpeed(double speed) {
        this.defaultRevertSpeed = speed;
        updateAllCaptureBlocks("revertSpeed", speed);
        setDirty();
    }

    public void setSingleRadius(BlockPos pos, double radius) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;

        ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
            updateSingleCaptureBlockInLevel(level, pos, "radius", radius);
        });
    }

    public void setSingleCaptureSpeed(BlockPos pos, double speed) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;

        ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
            updateSingleCaptureBlockInLevel(level, pos, "captureSpeed", speed);
        });
    }

    public void setSingleRevertSpeed(BlockPos pos, double speed) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;

        ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
            updateSingleCaptureBlockInLevel(level, pos, "revertSpeed", speed);
        });
    }

    public void registerCaptureBlock(BlockPos pos) {
        allCaptureBlocks.add(pos);
        setDirty();
    }

    public void unregisterCaptureBlock(BlockPos pos) {
        allCaptureBlocks.remove(pos);
        setDirty();
    }

    private void updateAllCaptureBlocks(String parameter, double value) {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;

        if (allCaptureBlocks.isEmpty()) {
            scanAndUpdateAllCaptureBlocks(parameter, value);
        } else {

            ServerLifecycleHooks.getCurrentServer().getAllLevels().forEach(level -> {
                for (BlockPos pos : allCaptureBlocks) {
                    updateSingleCaptureBlockInLevel(level, pos, parameter, value);
                }
            });
        }
    }

    private void scanAndUpdateAllCaptureBlocks(String parameter, double value) {

        System.out.println("[Torf Point War] 警告: 据点注册表为空，全局设置可能不会立即生效。");
        System.out.println("[Torf Point War] 建议使用 /tpwadmin cleanup 命令重新扫描据点，或重新创建据点。");

        setDirty();
    }

    private void updateSingleCaptureBlockInLevel(ServerLevel level, BlockPos pos, String parameter, double value) {
        BlockEntity blockEntity = level.getBlockEntity(pos);

        if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
            switch (parameter) {
                case "radius":
                    captureCore.setRadius(value);
                    break;
                case "captureSpeed":
                    captureCore.setCaptureSpeed(value);
                    break;
                case "revertSpeed":
                    captureCore.setRevertSpeed(value);
                    break;
            }
        }
    }

    public int getCaptureBlockCount() {
        return allCaptureBlocks.size();
    }

    public void cleanupInvalidBlocks() {
        if (ServerLifecycleHooks.getCurrentServer() == null) return;

        Set<BlockPos> toRemove = new HashSet<>();

        for (BlockPos pos : allCaptureBlocks) {
            boolean found = false;

            for (ServerLevel level : ServerLifecycleHooks.getCurrentServer().getAllLevels()) {
                BlockEntity blockEntity = level.getBlockEntity(pos);
                if (blockEntity instanceof CaptureCoreBlockEntity) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                toRemove.add(pos);
            }
        }

        allCaptureBlocks.removeAll(toRemove);
        if (!toRemove.isEmpty()) {
            setDirty();
        }
    }
}