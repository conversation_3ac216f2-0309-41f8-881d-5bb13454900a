package com.quasar.torfpointwar.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.quasar.torfpointwar.auth.AuthValidator;
import com.quasar.torfpointwar.auth.ServerAuthManager;
import com.quasar.torfpointwar.block.CaptureCore;
import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import com.quasar.torfpointwar.config.GlobalCaptureConfig;
import com.quasar.torfpointwar.registry.ModBlocks;
import com.quasar.torfpointwar.registry.ModItems;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.commands.arguments.coordinates.BlockPosArgument;
import net.minecraft.world.item.ItemStack;

import java.util.Collection;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;

import java.util.Map;
import java.util.UUID;

public class TorfPointWarCommands {

    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("tpwadmin")
            .requires(source -> source.hasPermission(2))
            .then(Commands.literal("create")
                .then(Commands.argument("name", StringArgumentType.string())
                    .executes(TorfPointWarCommands::createCaptureCore)
                    .then(Commands.argument("pos", BlockPosArgument.blockPos())
                        .executes(TorfPointWarCommands::createCaptureCoreAtPos))))
            .then(Commands.literal("create-captured")
                .then(Commands.literal("red")
                    .then(Commands.argument("name", StringArgumentType.string())
                        .executes(ctx -> createCapturedCore(ctx, TeamSystem.Team.RED))
                        .then(Commands.argument("pos", BlockPosArgument.blockPos())
                            .executes(ctx -> createCapturedCoreAtPos(ctx, TeamSystem.Team.RED)))))
                .then(Commands.literal("blue")
                    .then(Commands.argument("name", StringArgumentType.string())
                        .executes(ctx -> createCapturedCore(ctx, TeamSystem.Team.BLUE))
                        .then(Commands.argument("pos", BlockPosArgument.blockPos())
                            .executes(ctx -> createCapturedCoreAtPos(ctx, TeamSystem.Team.BLUE))))))
            .then(Commands.literal("config")
                .then(Commands.literal("global")
                    .then(Commands.literal("radius")
                        .then(Commands.argument("value", DoubleArgumentType.doubleArg(1.0, 500.0))
                            .executes(TorfPointWarCommands::setGlobalRadius)))
                    .then(Commands.literal("capture-speed")
                        .then(Commands.argument("value", DoubleArgumentType.doubleArg(0.1, 10.0))
                            .executes(TorfPointWarCommands::setGlobalCaptureSpeed)))
                    .then(Commands.literal("revert-speed")
                        .then(Commands.argument("value", DoubleArgumentType.doubleArg(0.1, 10.0))
                            .executes(TorfPointWarCommands::setGlobalRevertSpeed)))
                    .then(Commands.literal("show")
                        .executes(TorfPointWarCommands::showGlobalConfig)))
                .then(Commands.literal("single")
                    .then(Commands.literal("radius")
                        .then(Commands.argument("value", DoubleArgumentType.doubleArg(1.0, 500.0))
                            .executes(TorfPointWarCommands::setSingleRadius)))
                    .then(Commands.literal("capture-speed")
                        .then(Commands.argument("value", DoubleArgumentType.doubleArg(0.1, 10.0))
                            .executes(TorfPointWarCommands::setSingleCaptureSpeed)))
                    .then(Commands.literal("revert-speed")
                        .then(Commands.argument("value", DoubleArgumentType.doubleArg(0.1, 10.0))
                            .executes(TorfPointWarCommands::setSingleRevertSpeed)))))
            .then(Commands.literal("team")
                .then(Commands.literal("create")
                    .then(Commands.argument("team_name", StringArgumentType.word())
                        .then(Commands.argument("color", IntegerArgumentType.integer())
                            .executes(TorfPointWarCommands::createTeam))))
                .then(Commands.literal("list")
                    .executes(TorfPointWarCommands::listTeams))
                .then(Commands.literal("assign")
                    .then(Commands.argument("entities", EntityArgument.entities())
                        .then(Commands.literal("red")
                            .executes(ctx -> assignEntitiesToTeam(ctx, TeamSystem.Team.RED)))
                        .then(Commands.literal("blue")
                            .executes(ctx -> assignEntitiesToTeam(ctx, TeamSystem.Team.BLUE))))))
            .then(Commands.literal("reinforce")
                .executes(TorfPointWarCommands::activateAdminReinforcement))
            .then(Commands.literal("give")
                .then(Commands.literal("banner")
                    .then(Commands.argument("player", EntityArgument.player())
                        .then(Commands.literal("neutral")
                            .executes(ctx -> giveBanner(ctx, "neutral")))
                        .then(Commands.literal("red")
                            .executes(ctx -> giveBanner(ctx, "red")))
                        .then(Commands.literal("blue")
                            .executes(ctx -> giveBanner(ctx, "blue"))))))
            .then(Commands.literal("info")
                .executes(TorfPointWarCommands::showSystemInfo))
            .then(Commands.literal("cleanup")
                .executes(TorfPointWarCommands::cleanupSystem))
            .then(Commands.literal("auth")
                .then(Commands.literal("status")
                    .executes(TorfPointWarCommands::checkAuthStatus))
                .then(Commands.literal("sync")
                    .executes(TorfPointWarCommands::forceSyncAuth))));
    }

    private static int createCaptureCore(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        String name = StringArgumentType.getString(context, "name");
        ServerPlayer player = source.getPlayerOrException();
        Level level = source.getLevel();
        BlockPos pos = player.blockPosition();

        return createCaptureCoreAtPosition(source, level, pos, name);
    }

    private static int createCaptureCoreAtPos(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        String name = StringArgumentType.getString(context, "name");
        Level level = source.getLevel();
        BlockPos pos = BlockPosArgument.getBlockPos(context, "pos");

        return createCaptureCoreAtPosition(source, level, pos, name);
    }

    private static int createCaptureCoreAtPosition(CommandSourceStack source, Level level, BlockPos pos, String name) {

        BlockState captureCore = ModBlocks.CAPTURE_CORE.get().defaultBlockState();
        level.setBlock(pos, captureCore, 3);

        BlockEntity blockEntity = level.getBlockEntity(pos);
        if (blockEntity instanceof CaptureCoreBlockEntity captureCoreEntity) {
            captureCoreEntity.setCoreName(name);
        }

        source.sendSuccess(() -> Component.literal("已在 " + pos.getX() + " " + pos.getY() + " " + pos.getZ() + " 创建占领核心: " + name + "（使用全局默认配置）"), true);
        return 1;
    }

    private static int createCapturedCore(CommandContext<CommandSourceStack> context, TeamSystem.Team team) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        String name = StringArgumentType.getString(context, "name");
        ServerPlayer player = source.getPlayerOrException();
        Level level = source.getLevel();
        BlockPos pos = player.blockPosition();

        return createCapturedCoreAtPosition(source, level, pos, name, team);
    }

    private static int createCapturedCoreAtPos(CommandContext<CommandSourceStack> context, TeamSystem.Team team) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        String name = StringArgumentType.getString(context, "name");
        Level level = source.getLevel();
        BlockPos pos = BlockPosArgument.getBlockPos(context, "pos");

        return createCapturedCoreAtPosition(source, level, pos, name, team);
    }

    private static int createCapturedCoreAtPosition(CommandSourceStack source, Level level, BlockPos pos, String name, TeamSystem.Team team) {

        BlockState captureCore = ModBlocks.CAPTURE_CORE.get().defaultBlockState();

        if (team == TeamSystem.Team.RED) {
            captureCore = captureCore.setValue(CaptureCore.CAPTURE_STATE, CaptureCore.CaptureState.RED_CAPTURED);
        } else if (team == TeamSystem.Team.BLUE) {
            captureCore = captureCore.setValue(CaptureCore.CAPTURE_STATE, CaptureCore.CaptureState.BLUE_CAPTURED);
        }

        level.setBlock(pos, captureCore, 3);

        BlockEntity blockEntity = level.getBlockEntity(pos);
        if (blockEntity instanceof CaptureCoreBlockEntity captureCoreEntity) {
            captureCoreEntity.setCoreName(name);

            if (team == TeamSystem.Team.RED) {
                captureCoreEntity.setCaptureProgress(100.0);
            } else if (team == TeamSystem.Team.BLUE) {
                captureCoreEntity.setCaptureProgress(-100.0);
            }
            captureCoreEntity.setControllingTeam(team);
        }

        String teamName = team == TeamSystem.Team.RED ? "红队" : "蓝队";
        source.sendSuccess(() -> Component.literal("已在 " + pos.getX() + " " + pos.getY() + " " + pos.getZ() + " 创建" + teamName + "预占领核心: " + name + "（" + teamName + "完全控制）"), true);
        return 1;
    }

    private static int setGlobalRadius(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        double radius = DoubleArgumentType.getDouble(context, "value");

        GlobalCaptureConfig.getInstance().setGlobalRadius(radius);
        source.sendSuccess(() -> Component.literal("已设置全局默认半径为: " + radius + " （所有现有据点已同步更新）"), true);
        return 1;
    }

    private static int setGlobalCaptureSpeed(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        double speed = DoubleArgumentType.getDouble(context, "value");

        GlobalCaptureConfig.getInstance().setGlobalCaptureSpeed(speed);
        source.sendSuccess(() -> Component.literal("已设置全局默认占领速度为: " + speed + " （所有现有据点已同步更新）"), true);
        return 1;
    }

    private static int setGlobalRevertSpeed(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        double speed = DoubleArgumentType.getDouble(context, "value");

        GlobalCaptureConfig.getInstance().setGlobalRevertSpeed(speed);
        source.sendSuccess(() -> Component.literal("已设置全局默认回退速度为: " + speed + " （所有现有据点已同步更新）"), true);
        return 1;
    }

    private static int showGlobalConfig(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        GlobalCaptureConfig config = GlobalCaptureConfig.getInstance();

        source.sendSuccess(() -> Component.literal("=== 全局据点配置 ==="), false);
        source.sendSuccess(() -> Component.literal("默认半径: " + config.getDefaultRadius()), false);
        source.sendSuccess(() -> Component.literal("默认占领速度: " + config.getDefaultCaptureSpeed()), false);
        source.sendSuccess(() -> Component.literal("默认回退速度: " + config.getDefaultRevertSpeed()), false);
        source.sendSuccess(() -> Component.literal("已注册据点数量: " + config.getCaptureBlockCount()), false);
        return 1;
    }

    private static int setSingleRadius(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        double radius = DoubleArgumentType.getDouble(context, "value");
        ServerPlayer player = source.getPlayerOrException();

        CaptureCoreBlockEntity captureCore = findNearestCaptureCore(player);
        if (captureCore != null) {
            GlobalCaptureConfig.getInstance().setSingleRadius(captureCore.getBlockPos(), radius);
            source.sendSuccess(() -> Component.literal("已设置据点 " + captureCore.getCoreName() + " 的半径为: " + radius), true);
            return 1;
        } else {
            source.sendFailure(Component.literal("未找到附近的占领核心方块"));
            return 0;
        }
    }

    private static int setSingleCaptureSpeed(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        double speed = DoubleArgumentType.getDouble(context, "value");
        ServerPlayer player = source.getPlayerOrException();

        CaptureCoreBlockEntity captureCore = findNearestCaptureCore(player);
        if (captureCore != null) {
            GlobalCaptureConfig.getInstance().setSingleCaptureSpeed(captureCore.getBlockPos(), speed);
            source.sendSuccess(() -> Component.literal("已设置据点 " + captureCore.getCoreName() + " 的占领速度为: " + speed), true);
            return 1;
        } else {
            source.sendFailure(Component.literal("未找到附近的占领核心方块"));
            return 0;
        }
    }

    private static int setSingleRevertSpeed(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        double speed = DoubleArgumentType.getDouble(context, "value");
        ServerPlayer player = source.getPlayerOrException();

        CaptureCoreBlockEntity captureCore = findNearestCaptureCore(player);
        if (captureCore != null) {
            GlobalCaptureConfig.getInstance().setSingleRevertSpeed(captureCore.getBlockPos(), speed);
            source.sendSuccess(() -> Component.literal("已设置据点 " + captureCore.getCoreName() + " 的回退速度为: " + speed), true);
            return 1;
        } else {
            source.sendFailure(Component.literal("未找到附近的占领核心方块"));
            return 0;
        }
    }

    private static int createTeam(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        String teamName = StringArgumentType.getString(context, "team_name");
        int color = IntegerArgumentType.getInteger(context, "color");

        if (TeamSystem.createCustomTeam(teamName, color)) {
            source.sendSuccess(() -> Component.literal("已创建队伍: " + teamName), true);
            return 1;
        } else {
            source.sendFailure(Component.literal("队伍已存在: " + teamName));
            return 0;
        }
    }

    private static int listTeams(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        source.sendSuccess(() -> Component.literal("=== 队伍列表 ==="), false);
        source.sendSuccess(() -> Component.literal("基础队伍: RED, BLUE"), false);

        Map<String, TeamSystem.CustomTeam> customTeams = TeamSystem.getAllCustomTeams();
        if (!customTeams.isEmpty()) {
            source.sendSuccess(() -> Component.literal("自定义队伍:"), false);
            for (TeamSystem.CustomTeam team : customTeams.values()) {
                source.sendSuccess(() -> Component.literal("- " + team.getName() + " (成员: " + team.getMembers().size() + ")"), false);
            }
        }

        Map<UUID, TeamSystem.Team> entityTeams = TeamSystem.getAllEntityTeams();
        source.sendSuccess(() -> Component.literal("已分配实体数量: " + entityTeams.size()), false);

        return 1;
    }

    private static int assignEntitiesToTeam(CommandContext<CommandSourceStack> context, TeamSystem.Team team) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        Collection<? extends Entity> entities = EntityArgument.getEntities(context, "entities");

        int count = 0;
        for (Entity entity : entities) {

            TeamSystem.setEntityTeam(entity, team);

            count++;
        }

        final int finalCount = count;
        source.sendSuccess(() -> Component.literal("已将 " + finalCount + " 个实体分配到 " + team.getName() + " 队"), true);
        return 1;
    }

    private static int showSystemInfo(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        GlobalCaptureConfig config = GlobalCaptureConfig.getInstance();

        source.sendSuccess(() -> Component.literal("=== Torf Point War 系统信息 ==="), false);
        source.sendSuccess(() -> Component.literal("当前据点数量: " + config.getCaptureBlockCount()), false);

        Map<UUID, TeamSystem.Team> entityTeams = TeamSystem.getAllEntityTeams();
        final int redCount = (int) entityTeams.values().stream().filter(team -> team == TeamSystem.Team.RED).count();
        final int blueCount = (int) entityTeams.values().stream().filter(team -> team == TeamSystem.Team.BLUE).count();
        final int neutralCount = (int) entityTeams.values().stream().filter(team -> team == TeamSystem.Team.NEUTRAL).count();

        source.sendSuccess(() -> Component.literal("队伍分配: 红队(" + redCount + ") 蓝队(" + blueCount + ") 中立(" + neutralCount + ")"), false);
        source.sendSuccess(() -> Component.literal("全局配置: 半径(" + config.getDefaultRadius() + ") 占领速度(" + config.getDefaultCaptureSpeed() + ") 回退速度(" + config.getDefaultRevertSpeed() + ")"), false);

        source.sendSuccess(() -> Component.literal("=== 调试信息：已分配队伍的实体 ==="), false);
        for (Map.Entry<UUID, TeamSystem.Team> entry : entityTeams.entrySet()) {
            final UUID uuid = entry.getKey();
            final TeamSystem.Team team = entry.getValue();
            source.sendSuccess(() -> Component.literal("UUID: " + uuid + " -> 队伍: " + team.getName()), false);

            System.out.println("[TPW Debug] 已分配实体 - UUID: " + uuid + ", 队伍: " + team.getName());
        }

        return 1;
    }

    private static int cleanupSystem(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        GlobalCaptureConfig config = GlobalCaptureConfig.getInstance();

        int oldCount = config.getCaptureBlockCount();
        config.cleanupInvalidBlocks();
        int newCount = config.getCaptureBlockCount();
        int removedCount = oldCount - newCount;

        source.sendSuccess(() -> Component.literal("系统清理完成，移除了 " + removedCount + " 个无效据点记录"), true);
        return 1;
    }

    private static int activateAdminReinforcement(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        ServerPlayer player = source.getPlayerOrException();

        Level level = player.level();
        BlockPos playerPos = player.blockPosition();
        TeamSystem.Team playerTeam = TeamSystem.getEntityTeam(player);

        int searchRange = 100;
        for (int x = -searchRange; x <= searchRange; x++) {
            for (int y = -10; y <= 10; y++) {
                for (int z = -searchRange; z <= searchRange; z++) {
                    BlockPos checkPos = playerPos.offset(x, y, z);
                    BlockEntity blockEntity = level.getBlockEntity(checkPos);

                    if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                        double distance = player.distanceToSqr(checkPos.getX() + 0.5, checkPos.getY() + 0.5, checkPos.getZ() + 0.5);
                        double radius = captureCore.getRadius();

                        if (distance <= radius * radius) {

                            BlockState blockState = level.getBlockState(checkPos);
                            CaptureCore.CaptureState captureState = blockState.getValue(CaptureCore.CAPTURE_STATE);

                            boolean canUseReinforcement = false;
                            if (playerTeam == TeamSystem.Team.RED && captureState == CaptureCore.CaptureState.RED_CAPTURED) {
                                canUseReinforcement = true;
                            } else if (playerTeam == TeamSystem.Team.BLUE && captureState == CaptureCore.CaptureState.BLUE_CAPTURED) {
                                canUseReinforcement = true;
                            }

                            if (canUseReinforcement && !captureCore.isReinforced()) {
                                captureCore.activateReinforcement();
                                source.sendSuccess(() -> Component.literal("已激活增援效果！据点 " + captureCore.getCoreName() + " 将在2分钟内获得双倍防守加成。"), true);
                                return 1;
                            }
                        }
                    }
                }
            }
        }

        source.sendFailure(Component.literal("无法使用增援：你必须在己方完全占领的据点内才能使用此技能。"));
        return 0;
    }

    private static int giveBanner(CommandContext<CommandSourceStack> context, String bannerType) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();

        if (!AuthValidator.checkQuietly(source.getLevel())) {
            source.sendFailure(Component.literal("功能暂时不可用"));
            return 0;
        }

        ServerPlayer targetPlayer = EntityArgument.getPlayer(context, "player");

        ItemStack bannerStack;
        String bannerName;

        switch (bannerType.toLowerCase()) {
            case "neutral":
                bannerStack = new ItemStack(ModItems.NEUTRAL_REINFORCEMENT_BANNER.get());
                bannerName = "中立增援旗帜";
                break;
            case "red":
                bannerStack = new ItemStack(ModItems.RED_REINFORCEMENT_BANNER.get());
                bannerName = "红队增援旗帜";
                break;
            case "blue":
                bannerStack = new ItemStack(ModItems.BLUE_REINFORCEMENT_BANNER.get());
                bannerName = "蓝队增援旗帜";
                break;
            default:
                source.sendFailure(Component.literal("无效的旗帜类型: " + bannerType));
                return 0;
        }

        if (targetPlayer.getInventory().add(bannerStack)) {
            source.sendSuccess(() -> Component.literal("已给予 " + targetPlayer.getName().getString() + " 一个 " + bannerName), true);
            targetPlayer.sendSystemMessage(Component.literal("你获得了一个 " + bannerName + "！"));
            return 1;
        } else {
            source.sendFailure(Component.literal("玩家背包已满，无法给予物品"));
            return 0;
        }
    }

    private static CaptureCoreBlockEntity findNearestCaptureCore(ServerPlayer player) {
        Level level = player.level();
        BlockPos playerPos = player.blockPosition();

        int searchRange = 100;
        for (int x = -searchRange; x <= searchRange; x++) {
            for (int y = -10; y <= 10; y++) {
                for (int z = -searchRange; z <= searchRange; z++) {
                    BlockPos checkPos = playerPos.offset(x, y, z);
                    BlockEntity blockEntity = level.getBlockEntity(checkPos);
                    if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                        return captureCore;
                    }
                }
            }
        }
        return null;
    }

    private static int checkAuthStatus(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        Level level = source.getLevel();

        boolean serverAuth = AuthValidator.performAuthCheck(level, System.currentTimeMillis());
        boolean cachedAuth = ServerAuthManager.getCurrentAuthStatus();

        source.sendSuccess(() -> Component.literal("§6=== 授权状态检查 ==="), false);
        source.sendSuccess(() -> Component.literal("§7服务器端实时检查: " + (serverAuth ? "§a已授权" : "§c未授权")), false);
        source.sendSuccess(() -> Component.literal("§7缓存状态: " + (cachedAuth ? "§a已授权" : "§c未授权")), false);

        if (level.getServer() != null) {
            var scoreboard = level.getServer().getScoreboard();
            boolean hasBZ = scoreboard.getObjective("BZ") != null;
            boolean hasKevin = scoreboard.getObjective("Kevin") != null;

            source.sendSuccess(() -> Component.literal("§7计分板 'BZ': " + (hasBZ ? "§a存在" : "§c不存在")), false);
            source.sendSuccess(() -> Component.literal("§7计分板 'Kevin': " + (hasKevin ? "§a存在" : "§c不存在")), false);
        }

        return 1;
    }

    private static int forceSyncAuth(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        ServerAuthManager.forceSyncToAllPlayers();

        source.sendSuccess(() -> Component.literal("§a已强制同步授权状态给所有在线玩家"), false);
        return 1;
    }
}