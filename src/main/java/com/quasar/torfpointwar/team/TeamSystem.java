package com.quasar.torfpointwar.team;

import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.StringTag;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraftforge.server.ServerLifecycleHooks;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class TeamSystem extends SavedData {
    
    private static final String DATA_NAME = "torfpointwar_teams";
    private static TeamSystem instance;
    
    public enum Team {
        NEUTRAL("neutral", 0x808080),
        RED("red", 0xFF0000),
        BLUE("blue", 0x0000FF);
        
        private final String name;
        private final int color;
        
        Team(String name, int color) {
            this.name = name;
            this.color = color;
        }
        
        public String getName() { return name; }
        public int getColor() { return color; }
    }
    
    // 存储实体UUID到队伍的映射
    private final Map<UUID, Team> entityTeams = new ConcurrentHashMap<>();
    // 存储自定义队伍
    private final Map<String, CustomTeam> customTeams = new ConcurrentHashMap<>();
    
    public static class CustomTeam {
        private final String name;
        private final int color;
        private final Set<UUID> members;
        
        public CustomTeam(String name, int color) {
            this.name = name;
            this.color = color;
            this.members = ConcurrentHashMap.newKeySet();
        }
        
        public String getName() { return name; }
        public int getColor() { return color; }
        public Set<UUID> getMembers() { return members; }
    }
    
    public TeamSystem() {
        super();
    }
    
    public static TeamSystem getInstance() {
        if (instance == null) {
            ServerLevel overworld = ServerLifecycleHooks.getCurrentServer().overworld();
            instance = overworld.getDataStorage().computeIfAbsent(
                TeamSystem::load, TeamSystem::new, DATA_NAME);
        }
        return instance;
    }
    
    public static TeamSystem load(CompoundTag tag) {
        TeamSystem teamSystem = new TeamSystem();
        
        // 加载实体队伍映射
        if (tag.contains("EntityTeams")) {
            CompoundTag entityTeamsTag = tag.getCompound("EntityTeams");
            for (String uuidString : entityTeamsTag.getAllKeys()) {
                try {
                    UUID uuid = UUID.fromString(uuidString);
                    String teamName = entityTeamsTag.getString(uuidString);
                    Team team = Team.valueOf(teamName.toUpperCase());
                    teamSystem.entityTeams.put(uuid, team);
                } catch (Exception e) {
                    // 忽略无效的UUID或队伍名称
                }
            }
        }
        
        // 加载自定义队伍
        if (tag.contains("CustomTeams")) {
            CompoundTag customTeamsTag = tag.getCompound("CustomTeams");
            for (String teamName : customTeamsTag.getAllKeys()) {
                CompoundTag teamTag = customTeamsTag.getCompound(teamName);
                int color = teamTag.getInt("Color");
                CustomTeam customTeam = new CustomTeam(teamName, color);
                
                if (teamTag.contains("Members")) {
                    ListTag membersTag = teamTag.getList("Members", 8); // STRING类型
                    for (int i = 0; i < membersTag.size(); i++) {
                        try {
                            UUID uuid = UUID.fromString(membersTag.getString(i));
                            customTeam.getMembers().add(uuid);
                        } catch (Exception e) {
                            // 忽略无效的UUID
                        }
                    }
                }
                
                teamSystem.customTeams.put(teamName, customTeam);
            }
        }
        
        return teamSystem;
    }
    
    @Override
    public CompoundTag save(CompoundTag tag) {
        // 保存实体队伍映射
        CompoundTag entityTeamsTag = new CompoundTag();
        for (Map.Entry<UUID, Team> entry : entityTeams.entrySet()) {
            entityTeamsTag.putString(entry.getKey().toString(), entry.getValue().name());
        }
        tag.put("EntityTeams", entityTeamsTag);
        
        // 保存自定义队伍
        CompoundTag customTeamsTag = new CompoundTag();
        for (Map.Entry<String, CustomTeam> entry : customTeams.entrySet()) {
            CompoundTag teamTag = new CompoundTag();
            CustomTeam team = entry.getValue();
            teamTag.putInt("Color", team.getColor());
            
            ListTag membersTag = new ListTag();
            for (UUID uuid : team.getMembers()) {
                membersTag.add(StringTag.valueOf(uuid.toString()));
            }
            teamTag.put("Members", membersTag);
            
            customTeamsTag.put(entry.getKey(), teamTag);
        }
        tag.put("CustomTeams", customTeamsTag);
        
        return tag;
    }
    
    // 静态方法用于快速访问
    public static Team getEntityTeam(Entity entity) {
        return getInstance().entityTeams.getOrDefault(entity.getUUID(), Team.NEUTRAL);
    }
    
    public static void setEntityTeam(Entity entity, Team team) {
        TeamSystem system = getInstance();
        if (team == Team.NEUTRAL) {
            system.entityTeams.remove(entity.getUUID());
        } else {
            system.entityTeams.put(entity.getUUID(), team);
        }
        system.setDirty();
    }
    
    public static void setEntityTeam(UUID entityUuid, Team team) {
        TeamSystem system = getInstance();
        if (team == Team.NEUTRAL) {
            system.entityTeams.remove(entityUuid);
        } else {
            system.entityTeams.put(entityUuid, team);
        }
        system.setDirty();
    }
    
    public static boolean createCustomTeam(String name, int color) {
        TeamSystem system = getInstance();
        if (system.customTeams.containsKey(name)) {
            return false; // 队伍已存在
        }
        system.customTeams.put(name, new CustomTeam(name, color));
        system.setDirty();
        return true;
    }
    
    public static CustomTeam getCustomTeam(String name) {
        return getInstance().customTeams.get(name);
    }
    
    public static Map<String, CustomTeam> getAllCustomTeams() {
        return new HashMap<>(getInstance().customTeams);
    }
    
    public static Map<UUID, Team> getAllEntityTeams() {
        return new HashMap<>(getInstance().entityTeams);
    }
    
    public static void removeEntityFromAllTeams(UUID entityUuid) {
        TeamSystem system = getInstance();
        system.entityTeams.remove(entityUuid);
        for (CustomTeam team : system.customTeams.values()) {
            team.getMembers().remove(entityUuid);
        }
        system.setDirty();
    }
}