package com.quasar.torfpointwar.block;

import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import com.quasar.torfpointwar.registry.ModBlockEntities;
import net.minecraft.core.BlockPos;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.BaseEntityBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityTicker;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.phys.BlockHitResult;
import org.jetbrains.annotations.Nullable;

public class CaptureCore extends BaseEntityBlock {

    public static final EnumProperty<CaptureState> CAPTURE_STATE = EnumProperty.create("capture_state", CaptureState.class);

    public enum CaptureState implements net.minecraft.util.StringRepresentable {
        NEUTRAL("neutral"),
        RED_CAPTURED("red_captured"),
        BLUE_CAPTURED("blue_captured");

        private final String name;

        CaptureState(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }
    }

    public CaptureCore(Properties properties) {
        super(properties);
        this.registerDefaultState(this.stateDefinition.any().setValue(CAPTURE_STATE, CaptureState.NEUTRAL));
    }

    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        builder.add(CAPTURE_STATE);
    }

    @Nullable
    @Override
    public BlockEntity newBlockEntity(BlockPos pos, BlockState state) {
        return new CaptureCoreBlockEntity(pos, state);
    }

    @Override
    public RenderShape getRenderShape(BlockState state) {
        return RenderShape.MODEL;
    }

    @Override
    public InteractionResult use(BlockState state, Level level, BlockPos pos, Player player, InteractionHand hand, BlockHitResult hit) {
        if (!level.isClientSide) {

            net.minecraft.world.item.ItemStack itemInHand = player.getItemInHand(hand);
            if (itemInHand.getItem() instanceof com.quasar.torfpointwar.item.ReinforcementBanner banner) {

                return banner.useOnBlock(level, player, pos, hand) ? InteractionResult.SUCCESS : InteractionResult.FAIL;
            }

            BlockEntity blockEntity = level.getBlockEntity(pos);
            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                player.sendSystemMessage(net.minecraft.network.chat.Component.literal(
                    "占领核心: " + captureCore.getCoreName() +
                    " | 半径: " + captureCore.getRadius() +
                    " | 占领速度: " + captureCore.getCaptureSpeed() +
                    " | 回退速度: " + captureCore.getRevertSpeed()
                ));
            }
        }
        return InteractionResult.SUCCESS;
    }

    @Override
    public void setPlacedBy(Level level, BlockPos pos, BlockState state, @Nullable net.minecraft.world.entity.LivingEntity placer, ItemStack stack) {
        super.setPlacedBy(level, pos, state, placer, stack);

        if (!level.isClientSide) {
            BlockEntity blockEntity = level.getBlockEntity(pos);
            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                CaptureState captureState = state.getValue(CAPTURE_STATE);

                if (captureState == CaptureState.RED_CAPTURED) {
                    captureCore.setCaptureProgress(100.0);
                    captureCore.setControllingTeam(com.quasar.torfpointwar.team.TeamSystem.Team.RED);
                } else if (captureState == CaptureState.BLUE_CAPTURED) {
                    captureCore.setCaptureProgress(-100.0);
                    captureCore.setControllingTeam(com.quasar.torfpointwar.team.TeamSystem.Team.BLUE);
                } else {

                    captureCore.setCaptureProgress(0.0);
                    captureCore.setControllingTeam(com.quasar.torfpointwar.team.TeamSystem.Team.NEUTRAL);
                }
            }
        }
    }

    @Nullable
    @Override
    public <T extends BlockEntity> BlockEntityTicker<T> getTicker(Level level, BlockState state, BlockEntityType<T> blockEntityType) {
        return createTickerHelper(blockEntityType, ModBlockEntities.CAPTURE_CORE.get(),
            level.isClientSide ? CaptureCoreBlockEntity::clientTick : CaptureCoreBlockEntity::serverTick);
    }
}