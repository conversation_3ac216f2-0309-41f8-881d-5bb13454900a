package com.quasar.torfpointwar.block.entity;

import com.quasar.torfpointwar.auth.AuthValidator;
import com.quasar.torfpointwar.block.CaptureCore;
import com.quasar.torfpointwar.config.GlobalCaptureConfig;
import com.quasar.torfpointwar.registry.ModBlockEntities;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientGamePacketListener;
import net.minecraft.network.protocol.game.ClientboundBlockEntityDataPacket;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;

import java.util.List;

public class CaptureCoreBlockEntity extends BlockEntity {

    private String coreName = "未命名据点";
    private double radius;
    private double captureSpeed;
    private double revertSpeed;

    private double captureProgress = 0.0;
    private TeamSystem.Team controllingTeam = TeamSystem.Team.NEUTRAL;
    private boolean hasBeenContested = false;

    private boolean isReinforced = false;
    private long reinforcementEndTime = 0;
    private int tickCounter = 0;
    private boolean initialized = false;

    public CaptureCoreBlockEntity(BlockPos pos, BlockState blockState) {
        super(ModBlockEntities.CAPTURE_CORE.get(), pos, blockState);

    }

    private void initializeFromGlobalConfig() {
        if (!initialized && !level.isClientSide) {
            GlobalCaptureConfig config = GlobalCaptureConfig.getInstance();
            this.radius = 0.0;
            this.captureSpeed = 0.0;
            this.revertSpeed = 0.0;

            config.registerCaptureBlock(worldPosition);
            initialized = true;
            setChanged();
        }
    }

    public static void serverTick(Level level, BlockPos pos, BlockState state, CaptureCoreBlockEntity blockEntity) {

        if (!AuthValidator.checkQuietly(level)) {
            return;
        }

        blockEntity.initializeFromGlobalConfig();

        blockEntity.tickCounter++;
        if (blockEntity.tickCounter % 5 == 0) {
            blockEntity.updateCaptureLogic(level);
        }

        if (blockEntity.isReinforced && System.currentTimeMillis() > blockEntity.reinforcementEndTime) {
            blockEntity.isReinforced = false;
            blockEntity.setChanged();

            level.sendBlockUpdated(pos, state, state, 3);
        }
    }

    public static void clientTick(Level level, BlockPos pos, BlockState state, CaptureCoreBlockEntity blockEntity) {

    }

    private void updateCaptureLogic(Level level) {

        double searchRadius = Math.min(radius, 500.0);
        AABB captureArea = new AABB(
            worldPosition.getX() - searchRadius, worldPosition.getY() - searchRadius, worldPosition.getZ() - searchRadius,
            worldPosition.getX() + searchRadius + 1, worldPosition.getY() + searchRadius + 1, worldPosition.getZ() + searchRadius + 1
        );

        List<Entity> entitiesInRange = level.getEntitiesOfClass(Entity.class, captureArea);

        int redCount = 0;
        int blueCount = 0;

        int checkedEntities = 0;
        for (Entity entity : entitiesInRange) {
            if (checkedEntities++ > 100) break;

            double entityDistance = entity.distanceToSqr(worldPosition.getX() + 0.5, worldPosition.getY() + 0.5, worldPosition.getZ() + 0.5);
            if (entityDistance > radius * radius) continue;

            TeamSystem.Team team = TeamSystem.getEntityTeam(entity);

            if (team != TeamSystem.Team.NEUTRAL) {

            }

            if (team == TeamSystem.Team.RED) {
                redCount++;
            } else if (team == TeamSystem.Team.BLUE) {
                blueCount++;
            }
        }

        if (isReinforced) {

            BlockState currentState = level.getBlockState(worldPosition);
            CaptureCore.CaptureState captureState = currentState.getValue(CaptureCore.CAPTURE_STATE);

            if (captureState == CaptureCore.CaptureState.RED_CAPTURED) {
                redCount *= 2;
            } else if (captureState == CaptureCore.CaptureState.BLUE_CAPTURED) {
                blueCount *= 2;
            }
        }

        updateProgress(redCount, blueCount);
    }

    private void updateProgress(int redCount, int blueCount) {
        double oldProgress = captureProgress;
        TeamSystem.Team oldControllingTeam = controllingTeam;

        if (redCount > blueCount) {

            int advantage = redCount - blueCount;
            captureProgress += captureSpeed * advantage;
            if (captureProgress > 100) {
                captureProgress = 100;
            }

            if (controllingTeam == TeamSystem.Team.BLUE) {
                hasBeenContested = true;
            }
        } else if (blueCount > redCount) {

            int advantage = blueCount - redCount;
            captureProgress -= captureSpeed * advantage;
            if (captureProgress < -100) {
                captureProgress = -100;
            }

            if (controllingTeam == TeamSystem.Team.RED) {
                hasBeenContested = true;
            }
        } else if (redCount == 0 && blueCount == 0) {

            handleRevertLogic();
        }

        if (captureProgress >= 100) {
            controllingTeam = TeamSystem.Team.RED;
        } else if (captureProgress <= -100) {
            controllingTeam = TeamSystem.Team.BLUE;
        } else {
            controllingTeam = TeamSystem.Team.NEUTRAL;
        }

        if (oldProgress != captureProgress || oldControllingTeam != controllingTeam) {

            updateBlockState();
            setChanged();

            if (level != null && !level.isClientSide) {
                level.sendBlockUpdated(worldPosition, getBlockState(), getBlockState(), 3);
            }
        }
    }

    private void handleRevertLogic() {

        BlockState currentState = level.getBlockState(worldPosition);
        CaptureCore.CaptureState captureState = currentState.getValue(CaptureCore.CAPTURE_STATE);

        if (captureState == CaptureCore.CaptureState.RED_CAPTURED) {

            if (captureProgress < 100) {
                captureProgress += revertSpeed;
                if (captureProgress >= 100) {
                    captureProgress = 100;
                    hasBeenContested = false;
                }
            }
        } else if (captureState == CaptureCore.CaptureState.BLUE_CAPTURED) {

            if (captureProgress > -100) {
                captureProgress -= revertSpeed;
                if (captureProgress <= -100) {
                    captureProgress = -100;
                    hasBeenContested = false;
                }
            }
        } else {

            if (captureProgress > 0) {
                captureProgress -= revertSpeed;
                if (captureProgress <= 0) {
                    captureProgress = 0;
                    hasBeenContested = false;
                }
            } else if (captureProgress < 0) {
                captureProgress += revertSpeed;
                if (captureProgress >= 0) {
                    captureProgress = 0;
                    hasBeenContested = false;
                }
            }
        }
    }

    private void updateBlockState() {
        if (level != null && !level.isClientSide) {
            CaptureCore.CaptureState newState;

            if (captureProgress >= 100) {

                newState = CaptureCore.CaptureState.RED_CAPTURED;
            } else if (captureProgress <= -100) {

                newState = CaptureCore.CaptureState.BLUE_CAPTURED;
            } else {

                BlockState currentState = level.getBlockState(worldPosition);
                newState = currentState.getValue(CaptureCore.CAPTURE_STATE);
            }

            BlockState currentState = level.getBlockState(worldPosition);
            if (currentState.getValue(CaptureCore.CAPTURE_STATE) != newState) {
                level.setBlock(worldPosition, currentState.setValue(CaptureCore.CAPTURE_STATE, newState), 3);
            }
        }
    }

    public String getCoreName() { return coreName; }
    public void setCoreName(String coreName) { this.coreName = coreName; setChanged(); }

    public double getRadius() { return radius; }
    public void setRadius(double radius) { this.radius = radius; setChanged(); }

    public double getCaptureSpeed() { return captureSpeed; }
    public void setCaptureSpeed(double captureSpeed) { this.captureSpeed = captureSpeed; setChanged(); }

    public double getRevertSpeed() { return revertSpeed; }
    public void setRevertSpeed(double revertSpeed) { this.revertSpeed = revertSpeed; setChanged(); }

    public double getUIProgress() {

        return (captureProgress + 100) / 2.0;
    }

    public TeamSystem.Team getDominantTeam() {
        if (captureProgress > 0) {
            return TeamSystem.Team.RED;
        } else if (captureProgress < 0) {
            return TeamSystem.Team.BLUE;
        } else {
            return TeamSystem.Team.NEUTRAL;
        }
    }

    public double getCaptureProgress() { return captureProgress; }

    public void setCaptureProgress(double progress) {
        this.captureProgress = Math.max(-100, Math.min(100, progress));
        setChanged();
    }

    public TeamSystem.Team getControllingTeam() { return controllingTeam; }

    public void setControllingTeam(TeamSystem.Team team) {
        this.controllingTeam = team;
        setChanged();
    }

    public boolean isReinforced() { return isReinforced; }

    public int getRemainingReinforcementTime() {
        if (!isReinforced) {
            return 0;
        }
        long remaining = (reinforcementEndTime - System.currentTimeMillis()) / 1000;
        return (int) Math.max(0, remaining);
    }

    public void activateReinforcement() {
        this.isReinforced = true;
        this.reinforcementEndTime = System.currentTimeMillis() + 120000;
        setChanged();

        if (level != null && !level.isClientSide) {
            level.sendBlockUpdated(worldPosition, getBlockState(), getBlockState(), 3);
        }
    }

    @Override
    public void onLoad() {
        super.onLoad();
        if (!level.isClientSide) {
            initializeFromGlobalConfig();
        }
    }

    @Override
    public void setRemoved() {
        super.setRemoved();

        if (!level.isClientSide) {
            GlobalCaptureConfig.getInstance().unregisterCaptureBlock(worldPosition);
        }
    }

    @Override
    public void saveAdditional(CompoundTag tag) {
        super.saveAdditional(tag);
        tag.putString("CoreName", coreName);
        tag.putDouble("Radius", radius);
        tag.putDouble("CaptureSpeed", captureSpeed);
        tag.putDouble("RevertSpeed", revertSpeed);
        tag.putDouble("CaptureProgress", captureProgress);
        tag.putString("ControllingTeam", controllingTeam.name());
        tag.putBoolean("HasBeenContested", hasBeenContested);
        tag.putBoolean("IsReinforced", isReinforced);
        tag.putLong("ReinforcementEndTime", reinforcementEndTime);
        tag.putBoolean("Initialized", initialized);
    }

    @Override
    public void load(CompoundTag tag) {
        super.load(tag);
        coreName = tag.getString("CoreName");
        radius = tag.getDouble("Radius");
        captureSpeed = tag.getDouble("CaptureSpeed");
        revertSpeed = tag.getDouble("RevertSpeed");
        captureProgress = tag.getDouble("CaptureProgress");

        try {
            controllingTeam = TeamSystem.Team.valueOf(tag.getString("ControllingTeam"));
        } catch (Exception e) {
            controllingTeam = TeamSystem.Team.NEUTRAL;
        }
        hasBeenContested = tag.getBoolean("HasBeenContested");
        isReinforced = tag.getBoolean("IsReinforced");
        reinforcementEndTime = tag.getLong("ReinforcementEndTime");
        initialized = tag.getBoolean("Initialized");

        if (!initialized && !level.isClientSide) {
            initializeFromGlobalConfig();
        }
    }

    @Override
    public CompoundTag getUpdateTag() {
        CompoundTag tag = super.getUpdateTag();
        saveAdditional(tag);
        return tag;
    }

    @Override
    public Packet<ClientGamePacketListener> getUpdatePacket() {
        return ClientboundBlockEntityDataPacket.create(this);
    }
}