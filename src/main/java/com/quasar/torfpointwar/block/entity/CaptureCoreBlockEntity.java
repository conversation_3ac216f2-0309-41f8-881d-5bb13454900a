package com.quasar.torfpointwar.block.entity;

import com.quasar.torfpointwar.auth.AuthValidator;
import com.quasar.torfpointwar.block.CaptureCore;
import com.quasar.torfpointwar.config.GlobalCaptureConfig;
import com.quasar.torfpointwar.registry.ModBlockEntities;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientGamePacketListener;
import net.minecraft.network.protocol.game.ClientboundBlockEntityDataPacket;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;

import java.util.List;

public class CaptureCoreBlockEntity extends BlockEntity {
    
    private String coreName = "未命名据点";
    private double radius;
    private double captureSpeed;
    private double revertSpeed;
    
    // 战地式占领机制：-100到+100双向争夺系统
    private double captureProgress = 0.0; // -100到+100，负数为蓝方控制，正数为红方控制
    private TeamSystem.Team controllingTeam = TeamSystem.Team.NEUTRAL; // 当前完全控制方（进度达到±100时）
    private boolean hasBeenContested = false; // 标记是否有敌方进入过，用于控制回退逻辑
    
    private boolean isReinforced = false;
    private long reinforcementEndTime = 0;
    private int tickCounter = 0;
    private boolean initialized = false;
    
    public CaptureCoreBlockEntity(BlockPos pos, BlockState blockState) {
        super(ModBlockEntities.CAPTURE_CORE.get(), pos, blockState);
        // 构造函数中不直接初始化，等到第一次tick或数据加载时初始化
    }
    
    private void initializeFromGlobalConfig() {
        if (!initialized && !level.isClientSide) {
            GlobalCaptureConfig config = GlobalCaptureConfig.getInstance();
            this.radius = config.getDefaultRadius();
            this.captureSpeed = config.getDefaultCaptureSpeed();
            this.revertSpeed = config.getDefaultRevertSpeed();
            
            // 注册到全局配置
            config.registerCaptureBlock(worldPosition);
            initialized = true;
            setChanged();
        }
    }
    
    public static void serverTick(Level level, BlockPos pos, BlockState state, CaptureCoreBlockEntity blockEntity) {
        // 授权检查
        if (!AuthValidator.checkQuietly(level)) {
            return; // 静默跳过，不影响现有状态
        }
        
        // 确保初始化（只在第一次运行）
        blockEntity.initializeFromGlobalConfig();
        
        blockEntity.tickCounter++;
        if (blockEntity.tickCounter % 5 == 0) { // 每5 tick检测一次
            blockEntity.updateCaptureLogic(level);
        }
        
        // 检查增援效果是否过期
        if (blockEntity.isReinforced && System.currentTimeMillis() > blockEntity.reinforcementEndTime) {
            blockEntity.isReinforced = false;
            blockEntity.setChanged();
            // 立即同步到客户端
            level.sendBlockUpdated(pos, state, state, 3);
        }
    }
    
    public static void clientTick(Level level, BlockPos pos, BlockState state, CaptureCoreBlockEntity blockEntity) {
        // 客户端tick逻辑（如果需要的话）
    }
    
    private void updateCaptureLogic(Level level) {
        // 移除搜索半径的硬限制，支持大半径据点
        double searchRadius = Math.min(radius, 500.0); // 提高搜索半径上限到500格
        AABB captureArea = new AABB(
            worldPosition.getX() - searchRadius, worldPosition.getY() - searchRadius, worldPosition.getZ() - searchRadius,
            worldPosition.getX() + searchRadius + 1, worldPosition.getY() + searchRadius + 1, worldPosition.getZ() + searchRadius + 1
        );
        
        List<Entity> entitiesInRange = level.getEntitiesOfClass(Entity.class, captureArea);
        
        int redCount = 0;
        int blueCount = 0;
        
        // 优化：限制检查的实体数量，提前退出大量实体的情况
        int checkedEntities = 0;
        for (Entity entity : entitiesInRange) {
            if (checkedEntities++ > 100) break; // 服务器端最多检查100个实体
            
            // 精确距离检查
            double entityDistance = entity.distanceToSqr(worldPosition.getX() + 0.5, worldPosition.getY() + 0.5, worldPosition.getZ() + 0.5);
            if (entityDistance > radius * radius) continue;
            
            TeamSystem.Team team = TeamSystem.getEntityTeam(entity);
            
            // 只在兵力发生变化时输出调试信息，避免刷屏
            if (team != TeamSystem.Team.NEUTRAL) {
                // 先不输出每个实体的调试信息，避免聊天刷屏
            }
            
            if (team == TeamSystem.Team.RED) {
                redCount++;
            } else if (team == TeamSystem.Team.BLUE) {
                blueCount++;
            }
        }
        
        // 调试日志：输出最终的兵力统计 - 已关闭
        /*
        if (redCount > 0 || blueCount > 0) {
            if (level != null && !level.isClientSide && level.getServer() != null) {
                level.getServer().getPlayerList().broadcastSystemMessage(
                    Component.literal("[DEBUG] 兵力统计 - 红队: " + redCount + ", 蓝队: " + blueCount), false);
            }
        }
        */
        
        // 应用增援效果（只在这里应用一次）
        if (isReinforced) {
            // 基于方块状态判断增援效果应用给哪个队伍
            BlockState currentState = level.getBlockState(worldPosition);
            CaptureCore.CaptureState captureState = currentState.getValue(CaptureCore.CAPTURE_STATE);
            
            if (captureState == CaptureCore.CaptureState.RED_CAPTURED) {
                redCount *= 2;
            } else if (captureState == CaptureCore.CaptureState.BLUE_CAPTURED) {
                blueCount *= 2;
            }
        }
        
        updateProgress(redCount, blueCount);
    }
    
    private void updateProgress(int redCount, int blueCount) {
        double oldProgress = captureProgress;
        TeamSystem.Team oldControllingTeam = controllingTeam;
        
        // 调试信息：显示进度更新逻辑 - 已关闭
        /*
        if (level != null && !level.isClientSide && level.getServer() != null && (redCount > 0 || blueCount > 0)) {
            level.getServer().getPlayerList().broadcastSystemMessage(
                Component.literal("[DEBUG PROGRESS] 更新前: " + String.format("%.1f", captureProgress) + 
                    ", 红队: " + redCount + ", 蓝队: " + blueCount), false);
        }
        */
        
        // 战地式争夺机制：基于人数优势推进进度条
        if (redCount > blueCount) {
            // 红方优势，向正数推进
            int advantage = redCount - blueCount;
            captureProgress += captureSpeed * advantage;
            if (captureProgress > 100) {
                captureProgress = 100;
            }
            
            // 如果当前是蓝方控制区域，标记为被争夺
            if (controllingTeam == TeamSystem.Team.BLUE) {
                hasBeenContested = true;
            }
        } else if (blueCount > redCount) {
            // 蓝方优势，向负数推进
            int advantage = blueCount - redCount;
            captureProgress -= captureSpeed * advantage;
            if (captureProgress < -100) {
                captureProgress = -100;
            }
            
            // 如果当前是红方控制区域，标记为被争夺
            if (controllingTeam == TeamSystem.Team.RED) {
                hasBeenContested = true;
            }
        } else if (redCount == 0 && blueCount == 0) {
            // 只有在无人的情况下才回退，双方兵力相等时保持当前状态
            handleRevertLogic();
        }
        // 双方兵力相等但都不为0的情况：保持当前进度不变（不执行任何操作）
        
        // 更新控制状态
        if (captureProgress >= 100) {
            controllingTeam = TeamSystem.Team.RED;
        } else if (captureProgress <= -100) {
            controllingTeam = TeamSystem.Team.BLUE;
        } else {
            controllingTeam = TeamSystem.Team.NEUTRAL;
        }
        
        // 检查状态变化并更新方块状态
        if (oldProgress != captureProgress || oldControllingTeam != controllingTeam) {
            // 调试信息：显示进度变化 - 已关闭
            /*
            if (level != null && !level.isClientSide && level.getServer() != null) {
                level.getServer().getPlayerList().broadcastSystemMessage(
                    Component.literal("[DEBUG PROGRESS] 更新后: " + String.format("%.1f", captureProgress) + 
                        ", 控制队伍: " + controllingTeam.getName()), false);
            }
            */
            
            updateBlockState();
            setChanged();
            // 立即同步到客户端，确保UI实时更新
            if (level != null && !level.isClientSide) {
                level.sendBlockUpdated(worldPosition, getBlockState(), getBlockState(), 3);
            }
        }
    }
    
    // 处理回退逻辑：向方块现有颜色对应的方向回退
    private void handleRevertLogic() {
        // 获取当前方块状态，决定回退方向
        BlockState currentState = level.getBlockState(worldPosition);
        CaptureCore.CaptureState captureState = currentState.getValue(CaptureCore.CAPTURE_STATE);
        
        if (captureState == CaptureCore.CaptureState.RED_CAPTURED) {
            // 红色方块：向+100（红方完全控制）回退
            if (captureProgress < 100) {
                captureProgress += revertSpeed;
                if (captureProgress >= 100) {
                    captureProgress = 100;
                    hasBeenContested = false; // 回到完全控制后重置争夺标记
                }
            }
        } else if (captureState == CaptureCore.CaptureState.BLUE_CAPTURED) {
            // 蓝色方块：向-100（蓝方完全控制）回退
            if (captureProgress > -100) {
                captureProgress -= revertSpeed;
                if (captureProgress <= -100) {
                    captureProgress = -100;
                    hasBeenContested = false; // 回到完全控制后重置争夺标记
                }
            }
        } else {
            // 中立方块：向0（中立）回退（原有逻辑）
            if (captureProgress > 0) {
                captureProgress -= revertSpeed;
                if (captureProgress <= 0) {
                    captureProgress = 0;
                    hasBeenContested = false;
                }
            } else if (captureProgress < 0) {
                captureProgress += revertSpeed;
                if (captureProgress >= 0) {
                    captureProgress = 0;
                    hasBeenContested = false;
                }
            }
        }
    }
    
    private void updateBlockState() {
        if (level != null && !level.isClientSide) {
            CaptureCore.CaptureState newState;
            
            // 方块状态逻辑：只有完全占领时才改变方块状态
            // 未完全占领时保持原控制方的颜色，这样原控制方仍可使用增援技能
            if (captureProgress >= 100) {
                // 红方完全占领
                newState = CaptureCore.CaptureState.RED_CAPTURED;
            } else if (captureProgress <= -100) {
                // 蓝方完全占领
                newState = CaptureCore.CaptureState.BLUE_CAPTURED;
            } else {
                // 未完全占领的情况：保持当前方块状态不变
                // 这样原控制方仍然可以使用增援技能，直到被完全占领
                BlockState currentState = level.getBlockState(worldPosition);
                newState = currentState.getValue(CaptureCore.CAPTURE_STATE);
            }
            
            BlockState currentState = level.getBlockState(worldPosition);
            if (currentState.getValue(CaptureCore.CAPTURE_STATE) != newState) {
                level.setBlock(worldPosition, currentState.setValue(CaptureCore.CAPTURE_STATE, newState), 3);
            }
        }
    }
    
    // Getters and Setters
    public String getCoreName() { return coreName; }
    public void setCoreName(String coreName) { this.coreName = coreName; setChanged(); }
    
    public double getRadius() { return radius; }
    public void setRadius(double radius) { this.radius = radius; setChanged(); }
    
    public double getCaptureSpeed() { return captureSpeed; }
    public void setCaptureSpeed(double captureSpeed) { this.captureSpeed = captureSpeed; setChanged(); }
    
    public double getRevertSpeed() { return revertSpeed; }
    public void setRevertSpeed(double revertSpeed) { this.revertSpeed = revertSpeed; setChanged(); }
    
    // 获取UI显示用的进度（0-100%，战地式双向进度条）
    public double getUIProgress() {
        // 将-100到+100的范围转换为0到100的显示范围
        // -100 -> 0% (完全蓝方控制)
        // 0 -> 50% (中立)  
        // +100 -> 100% (完全红方控制)
        return (captureProgress + 100) / 2.0;
    }
    
    // 获取当前主导队伍（用于UI显示颜色）
    public TeamSystem.Team getDominantTeam() {
        if (captureProgress > 0) {
            return TeamSystem.Team.RED;
        } else if (captureProgress < 0) {
            return TeamSystem.Team.BLUE;
        } else {
            return TeamSystem.Team.NEUTRAL;
        }
    }
    
    public double getCaptureProgress() { return captureProgress; }
    
    public void setCaptureProgress(double progress) { 
        this.captureProgress = Math.max(-100, Math.min(100, progress)); 
        setChanged(); 
    }
    
    public TeamSystem.Team getControllingTeam() { return controllingTeam; }
    
    public void setControllingTeam(TeamSystem.Team team) { 
        this.controllingTeam = team; 
        setChanged(); 
    }
    
    public boolean isReinforced() { return isReinforced; }
    
    // 获取增援剩余时间（秒），如果没有增援则返回0
    public int getRemainingReinforcementTime() {
        if (!isReinforced) {
            return 0;
        }
        long remaining = (reinforcementEndTime - System.currentTimeMillis()) / 1000;
        return (int) Math.max(0, remaining);
    }
    
    public void activateReinforcement() {
        this.isReinforced = true;
        this.reinforcementEndTime = System.currentTimeMillis() + 120000; // 2分钟
        setChanged();
        // 立即同步到客户端
        if (level != null && !level.isClientSide) {
            level.sendBlockUpdated(worldPosition, getBlockState(), getBlockState(), 3);
        }
    }
    
    @Override
    public void onLoad() {
        super.onLoad();
        if (!level.isClientSide) {
            initializeFromGlobalConfig();
        }
    }
    
    @Override
    public void setRemoved() {
        super.setRemoved();
        // 从全局配置中移除
        if (!level.isClientSide) {
            GlobalCaptureConfig.getInstance().unregisterCaptureBlock(worldPosition);
        }
    }
    
    @Override
    public void saveAdditional(CompoundTag tag) {
        super.saveAdditional(tag);
        tag.putString("CoreName", coreName);
        tag.putDouble("Radius", radius);
        tag.putDouble("CaptureSpeed", captureSpeed);
        tag.putDouble("RevertSpeed", revertSpeed);
        tag.putDouble("CaptureProgress", captureProgress);
        tag.putString("ControllingTeam", controllingTeam.name());
        tag.putBoolean("HasBeenContested", hasBeenContested);
        tag.putBoolean("IsReinforced", isReinforced);
        tag.putLong("ReinforcementEndTime", reinforcementEndTime);
        tag.putBoolean("Initialized", initialized);
    }
    
    @Override
    public void load(CompoundTag tag) {
        super.load(tag);
        coreName = tag.getString("CoreName");
        radius = tag.getDouble("Radius");
        captureSpeed = tag.getDouble("CaptureSpeed");
        revertSpeed = tag.getDouble("RevertSpeed");
        captureProgress = tag.getDouble("CaptureProgress");
        
        // 加载队伍信息，默认为NEUTRAL
        try {
            controllingTeam = TeamSystem.Team.valueOf(tag.getString("ControllingTeam"));
        } catch (Exception e) {
            controllingTeam = TeamSystem.Team.NEUTRAL;
        }
        hasBeenContested = tag.getBoolean("HasBeenContested");
        isReinforced = tag.getBoolean("IsReinforced");
        reinforcementEndTime = tag.getLong("ReinforcementEndTime");
        initialized = tag.getBoolean("Initialized");
        
        // 如果没有初始化过，从全局配置加载默认值
        if (!initialized && !level.isClientSide) {
            initializeFromGlobalConfig();
        }
    }
    
    @Override
    public CompoundTag getUpdateTag() {
        CompoundTag tag = super.getUpdateTag();
        saveAdditional(tag);
        return tag;
    }
    
    @Override
    public Packet<ClientGamePacketListener> getUpdatePacket() {
        return ClientboundBlockEntityDataPacket.create(this);
    }
}