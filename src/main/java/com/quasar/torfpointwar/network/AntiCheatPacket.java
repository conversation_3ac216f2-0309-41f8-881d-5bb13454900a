package com.quasar.torfpointwar.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.network.NetworkEvent;

import java.util.UUID;
import java.util.function.Supplier;

/**
 * 反作弊网络包
 * 用于客户端向服务器报告可疑行为
 */
public class AntiCheatPacket {
    
    private final UUID playerUUID;
    private final String playerName;
    private final String cheatType;
    
    public AntiCheatPacket(UUID playerUUID, String playerName, String cheatType) {
        this.playerUUID = playerUUID;
        this.playerName = playerName;
        this.cheatType = cheatType;
    }
    
    public AntiCheatPacket(FriendlyByteBuf buf) {
        this.playerUUID = buf.readUUID();
        this.playerName = buf.readUtf();
        this.cheatType = buf.readUtf();
    }
    
    public void toBytes(FriendlyByteBuf buf) {
        buf.writeUUID(playerUUID);
        buf.writeUtf(playerName);
        buf.writeUtf(cheatType);
    }
    
    public boolean handle(Supplier<NetworkEvent.Context> supplier) {
        NetworkEvent.Context context = supplier.get();
        context.enqueueWork(() -> {
            // 在服务器端处理反作弊包
            AntiCheatHandler.handleCheatAttempt(playerUUID, playerName, cheatType, context);
        });
        return true;
    }
    
    // Getters
    public UUID getPlayerUUID() {
        return playerUUID;
    }
    
    public String getPlayerName() {
        return playerName;
    }
    
    public String getCheatType() {
        return cheatType;
    }
}
