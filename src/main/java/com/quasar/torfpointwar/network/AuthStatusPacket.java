package com.quasar.torfpointwar.network;

import com.quasar.torfpointwar.auth.ClientAuthCache;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 授权状态同步包
 * 用于服务器向客户端同步授权状态
 */
public class AuthStatusPacket {
    
    private final boolean authorized;
    
    public AuthStatusPacket(boolean authorized) {
        this.authorized = authorized;
    }
    
    public AuthStatusPacket(FriendlyByteBuf buf) {
        this.authorized = buf.readBoolean();
    }
    
    public void toBytes(FriendlyByteBuf buf) {
        buf.writeBoolean(authorized);
    }
    
    public boolean handle(Supplier<NetworkEvent.Context> supplier) {
        NetworkEvent.Context context = supplier.get();
        context.enqueueWork(() -> {
            // 在客户端更新授权状态缓存
            ClientAuthCache.setAuthorized(authorized);
        });
        return true;
    }
    
    public boolean isAuthorized() {
        return authorized;
    }
}
