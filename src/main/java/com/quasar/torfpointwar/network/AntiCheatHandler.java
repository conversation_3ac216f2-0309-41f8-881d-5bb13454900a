package com.quasar.torfpointwar.network;

import com.quasar.torfpointwar.TorfPointWar;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent;
import net.minecraftforge.server.ServerLifecycleHooks;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 反作弊处理器
 * 处理客户端发送的作弊尝试报告
 */
public class AntiCheatHandler {
    
    // 记录玩家作弊尝试次数
    private static final Map<UUID, Integer> cheatAttempts = new HashMap<>();
    
    // 作弊类型描述
    private static final Map<String, String> cheatDescriptions = new HashMap<>();
    
    static {
        cheatDescriptions.put("F3+B_HITBOX_ATTEMPT", "尝试开启实体碰撞箱透视");
    }
    
    /**
     * 处理作弊尝试
     */
    public static void handleCheatAttempt(UUID playerUUID, String playerName, String cheatType, NetworkEvent.Context context) {
        try {
            MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
            if (server == null) {
                return;
            }
            
            // 验证发送者是否为当前连接的玩家
            ServerPlayer sender = context.getSender();
            if (sender == null || !sender.getUUID().equals(playerUUID)) {
                TorfPointWar.LOGGER.warn("收到无效的反作弊包：UUID不匹配");
                return;
            }
            
            // 增加作弊尝试计数
            int attempts = cheatAttempts.getOrDefault(playerUUID, 0) + 1;
            cheatAttempts.put(playerUUID, attempts);
            
            // 获取作弊类型描述
            String description = cheatDescriptions.getOrDefault(cheatType, "未知作弊行为");
            
            // 构建广播消息
            Component broadcastMessage = Component.literal(
                String.format("§c[反作弊] §f玩家 §e%s §f%s §7(第%d次尝试)", 
                    playerName, description, attempts)
            );
            
            // 向所有玩家广播
            server.getPlayerList().broadcastSystemMessage(broadcastMessage, false);
            
            // 记录到服务器日志
            TorfPointWar.LOGGER.warn("玩家 {} ({}) {} (第{}次尝试)", 
                playerName, playerUUID, description, attempts);
            
            // 如果尝试次数过多，可以采取进一步措施
            if (attempts >= 5) {
                Component warningMessage = Component.literal(
                    String.format("§c[反作弊] §f玩家 §e%s §f多次尝试作弊，请管理员注意！", playerName)
                );
                server.getPlayerList().broadcastSystemMessage(warningMessage, false);
                
                TorfPointWar.LOGGER.error("玩家 {} ({}) 多次尝试作弊，建议管理员介入", 
                    playerName, playerUUID);
            }
            
        } catch (Exception e) {
            TorfPointWar.LOGGER.error("处理反作弊包时发生错误: ", e);
        }
    }
    
    /**
     * 重置玩家的作弊尝试计数
     */
    public static void resetCheatAttempts(UUID playerUUID) {
        cheatAttempts.remove(playerUUID);
    }
    
    /**
     * 获取玩家的作弊尝试次数
     */
    public static int getCheatAttempts(UUID playerUUID) {
        return cheatAttempts.getOrDefault(playerUUID, 0);
    }
    
    /**
     * 清理所有作弊记录
     */
    public static void clearAllRecords() {
        cheatAttempts.clear();
    }
}
