package com.quasar.torfpointwar.network;

import com.quasar.torfpointwar.TorfPointWar;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkDirection;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.simple.SimpleChannel;

import java.lang.reflect.Constructor;

public class NetworkHandler {

    private static final String PROTOCOL_VERSION = "1";
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        createResourceLocation(TorfPointWar.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );

    private static ResourceLocation createResourceLocation(String namespace, String path) {
        try {

            Constructor<ResourceLocation> constructor = ResourceLocation.class.getDeclaredConstructor(String.class, String.class);
            constructor.setAccessible(true);
            return constructor.newInstance(namespace, path);
        } catch (Exception e) {
            try {

                return (ResourceLocation) ResourceLocation.class.getMethod("fromNamespaceAndPath", String.class, String.class)
                    .invoke(null, namespace, path);
            } catch (Exception e2) {
                try {

                    ResourceLocation result = ResourceLocation.tryParse(namespace + ":" + path);
                    if (result != null) {
                        return result;
                    }
                } catch (Exception e3) {

                }

                throw new RuntimeException("无法创建ResourceLocation: " + namespace + ":" + path, e);
            }
        }
    }

    private static int packetId = 0;

    private static int id() {
        return packetId++;
    }

    public static void register() {

        INSTANCE.messageBuilder(AntiCheatPacket.class, id(), NetworkDirection.PLAY_TO_SERVER)
            .decoder(AntiCheatPacket::new)
            .encoder(AntiCheatPacket::toBytes)
            .consumerMainThread(AntiCheatPacket::handle)
            .add();

        INSTANCE.messageBuilder(AuthStatusPacket.class, id(), NetworkDirection.PLAY_TO_CLIENT)
            .decoder(AuthStatusPacket::new)
            .encoder(AuthStatusPacket::toBytes)
            .consumerMainThread(AuthStatusPacket::handle)
            .add();
    }

    public static <MSG> void sendToServer(MSG message) {
        INSTANCE.sendToServer(message);
    }

    public static <MSG> void sendToPlayer(MSG message, ServerPlayer player) {
        INSTANCE.send(PacketDistributor.PLAYER.with(() -> player), message);
    }

    public static <MSG> void sendToAllPlayers(MSG message) {
        INSTANCE.send(PacketDistributor.ALL.noArg(), message);
    }
}
