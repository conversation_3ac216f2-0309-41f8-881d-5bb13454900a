package com.quasar.torfpointwar.network;

import com.quasar.torfpointwar.TorfPointWar;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkDirection;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.simple.SimpleChannel;

/**
 * 网络处理器
 * 管理客户端和服务器之间的网络通信
 */
public class NetworkHandler {
    
    private static final String PROTOCOL_VERSION = "1";
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        new ResourceLocation(TorfPointWar.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    private static int packetId = 0;
    
    private static int id() {
        return packetId++;
    }
    
    public static void register() {
        // 注册反作弊包
        INSTANCE.messageBuilder(AntiCheatPacket.class, id(), NetworkDirection.PLAY_TO_SERVER)
            .decoder(AntiCheatPacket::new)
            .encoder(AntiCheatPacket::toBytes)
            .consumerMainThread(AntiCheatPacket::handle)
            .add();

        // 注册授权状态同步包
        INSTANCE.messageBuilder(AuthStatusPacket.class, id(), NetworkDirection.PLAY_TO_CLIENT)
            .decoder(AuthStatusPacket::new)
            .encoder(AuthStatusPacket::toBytes)
            .consumerMainThread(AuthStatusPacket::handle)
            .add();
    }
    
    public static <MSG> void sendToServer(MSG message) {
        INSTANCE.sendToServer(message);
    }
    
    public static <MSG> void sendToPlayer(MSG message, ServerPlayer player) {
        INSTANCE.send(PacketDistributor.PLAYER.with(() -> player), message);
    }
    
    public static <MSG> void sendToAllPlayers(MSG message) {
        INSTANCE.send(PacketDistributor.ALL.noArg(), message);
    }
}
