package com.quasar.torfpointwar.network;

import com.quasar.torfpointwar.TorfPointWar;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkDirection;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.PacketDistributor;
import net.minecraftforge.network.simple.SimpleChannel;

import java.lang.reflect.Constructor;

/**
 * 网络处理器
 * 管理客户端和服务器之间的网络通信
 */
public class NetworkHandler {
    
    private static final String PROTOCOL_VERSION = "1";
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        createResourceLocation(TorfPointWar.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );

    /**
     * 创建ResourceLocation的兼容性方法
     * 使用反射来处理不同版本的ResourceLocation构造方法
     */
    private static ResourceLocation createResourceLocation(String namespace, String path) {
        try {
            // 尝试使用构造函数（在某些版本中可用）
            Constructor<ResourceLocation> constructor = ResourceLocation.class.getDeclaredConstructor(String.class, String.class);
            constructor.setAccessible(true);
            return constructor.newInstance(namespace, path);
        } catch (Exception e) {
            try {
                // 尝试使用fromNamespaceAndPath方法（在较新版本中）
                return (ResourceLocation) ResourceLocation.class.getMethod("fromNamespaceAndPath", String.class, String.class)
                    .invoke(null, namespace, path);
            } catch (Exception e2) {
                try {
                    // 尝试使用tryParse方法作为最后的备选
                    ResourceLocation result = ResourceLocation.tryParse(namespace + ":" + path);
                    if (result != null) {
                        return result;
                    }
                } catch (Exception e3) {
                    // 忽略
                }
                // 如果所有方法都失败，抛出异常
                throw new RuntimeException("无法创建ResourceLocation: " + namespace + ":" + path, e);
            }
        }
    }
    
    private static int packetId = 0;
    
    private static int id() {
        return packetId++;
    }
    
    public static void register() {
        // 注册反作弊包
        INSTANCE.messageBuilder(AntiCheatPacket.class, id(), NetworkDirection.PLAY_TO_SERVER)
            .decoder(AntiCheatPacket::new)
            .encoder(AntiCheatPacket::toBytes)
            .consumerMainThread(AntiCheatPacket::handle)
            .add();

        // 注册授权状态同步包
        INSTANCE.messageBuilder(AuthStatusPacket.class, id(), NetworkDirection.PLAY_TO_CLIENT)
            .decoder(AuthStatusPacket::new)
            .encoder(AuthStatusPacket::toBytes)
            .consumerMainThread(AuthStatusPacket::handle)
            .add();
    }
    
    public static <MSG> void sendToServer(MSG message) {
        INSTANCE.sendToServer(message);
    }
    
    public static <MSG> void sendToPlayer(MSG message, ServerPlayer player) {
        INSTANCE.send(PacketDistributor.PLAYER.with(() -> player), message);
    }
    
    public static <MSG> void sendToAllPlayers(MSG message) {
        INSTANCE.send(PacketDistributor.ALL.noArg(), message);
    }
}
