package com.quasar.torfpointwar.client;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.client.event.KeyInputHandler;
import com.quasar.torfpointwar.client.ui.CaptureUIRenderer;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;

@Mod.EventBusSubscriber(modid = TorfPointWar.MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class ClientModEvents {
    
    @SubscribeEvent
    public static void onClientSetup(FMLClientSetupEvent event) {
        event.enqueueWork(() -> {
            // 注册UI渲染器
            MinecraftForge.EVENT_BUS.register(CaptureUIRenderer.class);

            // 注册F3+B禁用处理器（使用反射强制禁用）
            MinecraftForge.EVENT_BUS.register(KeyInputHandler.class);
        });
    }
}