package com.quasar.torfpointwar.client;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.auth.ClientAuthCache;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.ClientPlayerNetworkEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = TorfPointWar.MODID, value = Dist.CLIENT)
public class ClientConnectionHandler {

    @SubscribeEvent
    public static void onClientDisconnect(ClientPlayerNetworkEvent.LoggingOut event) {

        ClientAuthCache.reset();
        TorfPointWar.LOGGER.info("客户端断开连接，授权状态已重置");
    }
}
