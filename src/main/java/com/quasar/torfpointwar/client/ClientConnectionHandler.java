package com.quasar.torfpointwar.client;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.auth.ClientAuthCache;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.ClientPlayerNetworkEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 客户端连接处理器
 * 处理客户端连接和断开连接事件
 */
@Mod.EventBusSubscriber(modid = TorfPointWar.MODID, value = Dist.CLIENT)
public class ClientConnectionHandler {
    
    @SubscribeEvent
    public static void onClientDisconnect(ClientPlayerNetworkEvent.LoggingOut event) {
        // 客户端断开连接时重置授权状态
        ClientAuthCache.reset();
        TorfPointWar.LOGGER.info("客户端断开连接，授权状态已重置");
    }
}
