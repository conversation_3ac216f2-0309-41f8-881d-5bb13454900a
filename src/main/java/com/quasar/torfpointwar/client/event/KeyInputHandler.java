package com.quasar.torfpointwar.client.event;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.network.AntiCheatPacket;
import com.quasar.torfpointwar.network.NetworkHandler;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import org.lwjgl.glfw.GLFW;

import java.lang.reflect.Field;

/**
 * 客户端键盘输入事件处理器
 * 主要用于禁用F3+B实体碰撞箱显示功能，防止透视作弊
 * 使用反射和强制禁用的方法来确保F3+B无法工作
 */
@Mod.EventBusSubscriber(modid = TorfPointWar.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class KeyInputHandler {

    private static final Minecraft minecraft = Minecraft.getInstance();
    private static boolean f3BPressed = false;
    private static long lastF3BTime = 0;
    private static Field renderHitBoxesField = null;

    static {
        // 初始化反射字段
        try {
            renderHitBoxesField = EntityRenderDispatcher.class.getDeclaredField("renderHitBoxes");
            renderHitBoxesField.setAccessible(true);
        } catch (Exception e) {
            // 尝试混淆名称
            try {
                renderHitBoxesField = EntityRenderDispatcher.class.getDeclaredField("f_114476_");
                renderHitBoxesField.setAccessible(true);
            } catch (Exception e2) {
                TorfPointWar.LOGGER.error("无法找到renderHitBoxes字段: ", e2);
            }
        }
    }

    /**
     * 使用客户端Tick事件来强制禁用实体碰撞箱渲染
     * 每个tick都检查并强制禁用，确保F3+B无法工作
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) {
            return;
        }

        try {
            // 只在游戏内处理，避免在菜单界面误触发
            if (minecraft.player == null || minecraft.level == null) {
                return;
            }

            EntityRenderDispatcher dispatcher = minecraft.getEntityRenderDispatcher();

            // 检查是否启用了碰撞箱渲染
            if (dispatcher.shouldRenderHitBoxes()) {
                // 强制禁用
                forceDisableHitBoxes(dispatcher);

                // 获取当前窗口句柄
                long windowHandle = minecraft.getWindow().getWindow();

                // 检查F3和B键是否同时被按下
                boolean f3Pressed = GLFW.glfwGetKey(windowHandle, GLFW.GLFW_KEY_F3) == GLFW.GLFW_PRESS;
                boolean bPressed = GLFW.glfwGetKey(windowHandle, GLFW.GLFW_KEY_B) == GLFW.GLFW_PRESS;

                // 如果同时按下F3和B键或者碰撞箱被启用
                if ((f3Pressed && bPressed) || dispatcher.shouldRenderHitBoxes()) {
                    long currentTime = System.currentTimeMillis();

                    // 防止重复触发（每1000ms最多触发一次）
                    if (!f3BPressed || (currentTime - lastF3BTime) > 1000) {
                        f3BPressed = true;
                        lastF3BTime = currentTime;

                        // 向玩家显示本地提示
                        if (minecraft.player != null) {
                            minecraft.player.displayClientMessage(
                                net.minecraft.network.chat.Component.literal("§c实体碰撞箱显示已被禁用！"),
                                true // 显示在ActionBar上
                            );

                            // 记录到日志
                            TorfPointWar.LOGGER.info("玩家 {} 尝试使用F3+B透视功能，已被阻止",
                                minecraft.player.getName().getString());

                            // 尝试发送网络包（如果在多人游戏中）
                            try {
                                if (minecraft.getCurrentServer() != null) {
                                    NetworkHandler.sendToServer(new AntiCheatPacket(
                                        minecraft.player.getUUID(),
                                        minecraft.player.getName().getString(),
                                        "F3+B_HITBOX_ATTEMPT"
                                    ));
                                }
                            } catch (Exception netEx) {
                                TorfPointWar.LOGGER.warn("无法发送反作弊网络包: {}", netEx.getMessage());
                            }
                        }
                    }
                } else {
                    // 重置状态
                    if (f3BPressed && (!f3Pressed || !bPressed)) {
                        f3BPressed = false;
                    }
                }
            }
        } catch (Exception e) {
            // 如果发生任何异常，记录错误但不崩溃游戏
            TorfPointWar.LOGGER.error("F3+B检测时发生错误: ", e);
        }
    }

    /**
     * 使用反射强制禁用实体碰撞箱渲染
     */
    private static void forceDisableHitBoxes(EntityRenderDispatcher dispatcher) {
        try {
            // 使用公共方法禁用
            dispatcher.setRenderHitBoxes(false);

            // 使用反射确保字段被设置为false
            if (renderHitBoxesField != null) {
                renderHitBoxesField.setBoolean(dispatcher, false);
            }
        } catch (Exception e) {
            TorfPointWar.LOGGER.debug("反射禁用碰撞箱时发生错误: ", e);
        }
    }
}
