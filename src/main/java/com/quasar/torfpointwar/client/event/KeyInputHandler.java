package com.quasar.torfpointwar.client.event;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.network.AntiCheatPacket;
import com.quasar.torfpointwar.network.NetworkHandler;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import org.lwjgl.glfw.GLFW;

import java.lang.reflect.Field;

@Mod.EventBusSubscriber(modid = TorfPointWar.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class KeyInputHandler {

    private static final Minecraft minecraft = Minecraft.getInstance();
    private static boolean f3BPressed = false;
    private static long lastF3BTime = 0;
    private static Field renderHitBoxesField = null;

    static {

        try {
            renderHitBoxesField = EntityRenderDispatcher.class.getDeclaredField("renderHitBoxes");
            renderHitBoxesField.setAccessible(true);
        } catch (Exception e) {

            try {
                renderHitBoxesField = EntityRenderDispatcher.class.getDeclaredField("f_114476_");
                renderHitBoxesField.setAccessible(true);
            } catch (Exception e2) {
                TorfPointWar.LOGGER.error("无法找到renderHitBoxes字段: ", e2);
            }
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) {
            return;
        }

        try {

            if (minecraft.player == null || minecraft.level == null) {
                return;
            }

            EntityRenderDispatcher dispatcher = minecraft.getEntityRenderDispatcher();

            if (dispatcher.shouldRenderHitBoxes()) {

                forceDisableHitBoxes(dispatcher);

                long windowHandle = minecraft.getWindow().getWindow();

                boolean f3Pressed = GLFW.glfwGetKey(windowHandle, GLFW.GLFW_KEY_F3) == GLFW.GLFW_PRESS;
                boolean bPressed = GLFW.glfwGetKey(windowHandle, GLFW.GLFW_KEY_B) == GLFW.GLFW_PRESS;

                if ((f3Pressed && bPressed) || dispatcher.shouldRenderHitBoxes()) {
                    long currentTime = System.currentTimeMillis();

                    if (!f3BPressed || (currentTime - lastF3BTime) > 1000) {
                        f3BPressed = true;
                        lastF3BTime = currentTime;

                        if (minecraft.player != null) {
                            minecraft.player.displayClientMessage(
                                net.minecraft.network.chat.Component.literal("§c实体碰撞箱显示已被禁用！"),
                                true
                            );

                            TorfPointWar.LOGGER.info("玩家 {} 尝试使用F3+B透视功能，已被阻止",
                                minecraft.player.getName().getString());

                            try {
                                if (minecraft.getCurrentServer() != null) {
                                    NetworkHandler.sendToServer(new AntiCheatPacket(
                                        minecraft.player.getUUID(),
                                        minecraft.player.getName().getString(),
                                        "F3+B_HITBOX_ATTEMPT"
                                    ));
                                }
                            } catch (Exception netEx) {
                                TorfPointWar.LOGGER.warn("无法发送反作弊网络包: {}", netEx.getMessage());
                            }
                        }
                    }
                } else {

                    if (f3BPressed && (!f3Pressed || !bPressed)) {
                        f3BPressed = false;
                    }
                }
            }
        } catch (Exception e) {

            TorfPointWar.LOGGER.error("F3+B检测时发生错误: ", e);
        }
    }

    private static void forceDisableHitBoxes(EntityRenderDispatcher dispatcher) {
        try {

            dispatcher.setRenderHitBoxes(false);

            if (renderHitBoxesField != null) {
                renderHitBoxesField.setBoolean(dispatcher, false);
            }
        } catch (Exception e) {
            TorfPointWar.LOGGER.debug("反射禁用碰撞箱时发生错误: ", e);
        }
    }
}
