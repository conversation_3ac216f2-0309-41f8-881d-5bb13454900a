package com.quasar.torfpointwar.client.ui;

import com.quasar.torfpointwar.auth.AuthValidator;
import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;

public class CaptureUIRenderer {

    private static final Minecraft minecraft = Minecraft.getInstance();
    private static final Map<BlockPos, CaptureUIInfo> activeCaptureUIs = new HashMap<>();
    private static final Set<BlockPos> knownCaptureBlocks = new HashSet<>();
    private static int updateCounter = 0;
    private static final int UPDATE_INTERVAL = 10;

    public static class CaptureUIInfo {
        public final CaptureCoreBlockEntity blockEntity;
        public final double distance;
        public final String coreName;
        public final double progress;
        public final TeamSystem.Team controllingTeam;
        public final TeamSystem.Team dominantTeam;
        public final int redCount;
        public final int blueCount;
        public final int reinforcementTime;
        public final TeamSystem.Team reinforcingTeam;

        public CaptureUIInfo(CaptureCoreBlockEntity blockEntity, double distance, String coreName,
                           double progress, TeamSystem.Team controllingTeam, TeamSystem.Team dominantTeam,
                           int redCount, int blueCount, int reinforcementTime, TeamSystem.Team reinforcingTeam) {
            this.blockEntity = blockEntity;
            this.distance = distance;
            this.coreName = coreName;
            this.progress = progress;
            this.controllingTeam = controllingTeam;
            this.dominantTeam = dominantTeam;
            this.redCount = redCount;
            this.blueCount = blueCount;
            this.reinforcementTime = reinforcementTime;
            this.reinforcingTeam = reinforcingTeam;
        }
    }

    @SubscribeEvent
    public static void onRenderGuiOverlay(RenderGuiOverlayEvent.Post event) {
        if (minecraft.player == null || minecraft.level == null) return;
        if (!AuthValidator.isAuthorized(minecraft.level)) return;

        updateCounter++;
        if (updateCounter >= UPDATE_INTERVAL) {
            updateCounter = 0;
            updateCaptureUIs();
        }

        return;
    }

    private static void updateCaptureUIs() {
        Map<BlockPos, CaptureUIInfo> newUIs = new HashMap<>();

        Player player = minecraft.player;
        Level level = minecraft.level;
        if (player == null || level == null) return;

        BlockPos playerPos = player.blockPosition();

        if (updateCounter % 200 == 0) {
            updateKnownCaptureBlocks(level, playerPos);
        }

        for (BlockPos checkPos : knownCaptureBlocks) {

            double distanceSq = player.distanceToSqr(checkPos.getX() + 0.5, checkPos.getY() + 0.5, checkPos.getZ() + 0.5);
            if (distanceSq > 250000) continue;

            BlockEntity blockEntity = level.getBlockEntity(checkPos);
            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                double radius = captureCore.getRadius();

                if (distanceSq <= radius * radius) {

                    EntityCount entityCount = countEntitiesEfficiently(level, checkPos, radius);

                    double currentProgress = captureCore.getUIProgress();

                    int reinforcementTime = captureCore.getRemainingReinforcementTime();

                    TeamSystem.Team reinforcingTeam = TeamSystem.Team.NEUTRAL;
                    if (reinforcementTime > 0) {

                        net.minecraft.world.level.block.state.BlockState blockState = level.getBlockState(checkPos);
                        com.quasar.torfpointwar.block.CaptureCore.CaptureState captureState = blockState.getValue(com.quasar.torfpointwar.block.CaptureCore.CAPTURE_STATE);

                        if (captureState == com.quasar.torfpointwar.block.CaptureCore.CaptureState.RED_CAPTURED) {
                            reinforcingTeam = TeamSystem.Team.RED;
                        } else if (captureState == com.quasar.torfpointwar.block.CaptureCore.CaptureState.BLUE_CAPTURED) {
                            reinforcingTeam = TeamSystem.Team.BLUE;
                        }
                    }

                    newUIs.put(checkPos, new CaptureUIInfo(
                        captureCore,
                        Math.sqrt(distanceSq),
                        captureCore.getCoreName(),
                        currentProgress,
                        captureCore.getControllingTeam(),
                        captureCore.getDominantTeam(),
                        entityCount.redCount,
                        entityCount.blueCount,
                        reinforcementTime,
                        reinforcingTeam
                    ));
                }
            }
        }

        activeCaptureUIs.clear();
        activeCaptureUIs.putAll(newUIs);
    }

    private static void updateKnownCaptureBlocks(Level level, BlockPos playerPos) {

        int smallRange = 15;
        for (int x = -smallRange; x <= smallRange; x++) {
            for (int y = -8; y <= 8; y++) {
                for (int z = -smallRange; z <= smallRange; z++) {
                    BlockPos checkPos = playerPos.offset(x, y, z);
                    BlockEntity blockEntity = level.getBlockEntity(checkPos);
                    if (blockEntity instanceof CaptureCoreBlockEntity) {
                        knownCaptureBlocks.add(checkPos);
                    }
                }
            }
        }

        int largeRange = 80;
        int step = 8;
        for (int x = -largeRange; x <= largeRange; x += step) {
            for (int y = -10; y <= 10; y += 4) {
                for (int z = -largeRange; z <= largeRange; z += step) {

                    if (Math.abs(x) <= smallRange && Math.abs(z) <= smallRange) continue;

                    BlockPos checkPos = playerPos.offset(x, y, z);
                    BlockEntity blockEntity = level.getBlockEntity(checkPos);
                    if (blockEntity instanceof CaptureCoreBlockEntity) {
                        knownCaptureBlocks.add(checkPos);

                        for (int dx = -2; dx <= 2; dx++) {
                            for (int dy = -2; dy <= 2; dy++) {
                                for (int dz = -2; dz <= 2; dz++) {
                                    BlockPos nearbyPos = checkPos.offset(dx, dy, dz);
                                    BlockEntity nearbyEntity = level.getBlockEntity(nearbyPos);
                                    if (nearbyEntity instanceof CaptureCoreBlockEntity) {
                                        knownCaptureBlocks.add(nearbyPos);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        knownCaptureBlocks.removeIf(pos -> {
            double distSq = playerPos.distSqr(pos);
            return distSq > 250000;
        });
    }

    private static class EntityCount {
        final int redCount;
        final int blueCount;

        EntityCount(int redCount, int blueCount) {
            this.redCount = redCount;
            this.blueCount = blueCount;
        }
    }

    private static EntityCount countEntitiesEfficiently(Level level, BlockPos center, double radius) {

        AABB captureArea = new AABB(
            center.getX() - radius, center.getY() - radius, center.getZ() - radius,
            center.getX() + radius + 1, center.getY() + radius + 1, center.getZ() + radius + 1
        );

        List<Entity> entitiesInRange = level.getEntitiesOfClass(Entity.class, captureArea);
        int redCount = 0;
        int blueCount = 0;

        int checkedEntities = 0;
        for (Entity entity : entitiesInRange) {
            if (checkedEntities++ > 30) break;

            double entityDistance = entity.distanceToSqr(center.getX() + 0.5, center.getY() + 0.5, center.getZ() + 0.5);
            if (entityDistance > radius * radius) continue;

            TeamSystem.Team team = TeamSystem.getEntityTeam(entity);
            if (team == TeamSystem.Team.RED) {
                redCount++;
            } else if (team == TeamSystem.Team.BLUE) {
                blueCount++;
            }
        }

        return new EntityCount(redCount, blueCount);
    }

    private static void renderCaptureUIs(GuiGraphics guiGraphics) {
        if (activeCaptureUIs.isEmpty()) return;

        List<CaptureUIInfo> sortedUIs = activeCaptureUIs.values().stream()
            .sorted(Comparator.comparingDouble(ui -> ui.distance))
            .limit(2)
            .toList();

        int screenWidth = minecraft.getWindow().getGuiScaledWidth();
        int yOffset = 10;

        for (int i = 0; i < sortedUIs.size(); i++) {
            CaptureUIInfo uiInfo = sortedUIs.get(i);

            int spacing = uiInfo.reinforcementTime > 0 ? 50 : 35;
            renderSingleCaptureUI(guiGraphics, uiInfo, screenWidth, yOffset + i * spacing);
        }
    }

    private static void renderSingleCaptureUI(GuiGraphics guiGraphics, CaptureUIInfo uiInfo, int screenWidth, int yPos) {
        int barWidth = 200;
        int barHeight = 6;
        int centerX = screenWidth / 2;
        int barX = centerX - barWidth / 2;

        guiGraphics.fill(barX - 3, yPos - 3, barX + barWidth + 3, yPos + barHeight + 3, 0xFF000000);

        guiGraphics.fill(barX - 2, yPos - 2, barX + barWidth + 2, yPos + barHeight + 2, 0xFF404040);

        guiGraphics.fill(barX, yPos, barX + barWidth, yPos + barHeight, 0x80202020);

        double progress = uiInfo.progress;
        int centerX_bar = barX + barWidth / 2;

        int fillColor;
        int fillStart, fillEnd;

        if (progress < 50) {

            fillColor = 0xFF0000FF;
            double blueRatio = (50 - progress) / 50.0;
            int blueWidth = (int) (barWidth / 2 * blueRatio);
            fillStart = barX;
            fillEnd = barX + blueWidth;
        } else if (progress > 50) {

            fillColor = 0xFFFF0000;
            double redRatio = (progress - 50) / 50.0;
            int redWidth = (int) (barWidth / 2 * redRatio);
            fillStart = centerX_bar;
            fillEnd = centerX_bar + redWidth;
        } else {

            fillStart = fillEnd = centerX_bar;
            fillColor = 0x80808080;
        }

        guiGraphics.fill(centerX_bar - 1, yPos, centerX_bar + 1, yPos + barHeight, 0xFFFFFFFF);

        if (fillEnd > fillStart) {
            guiGraphics.fill(fillStart, yPos, fillEnd, yPos + barHeight, fillColor);
        }

        String leftIndicator = generateForceIndicator(uiInfo.redCount, true);
        String rightIndicator = generateForceIndicator(uiInfo.blueCount, false);

        String coreText = " 【" + uiInfo.coreName + "】 争夺中 ";
        int coreTextWidth = minecraft.font.width(coreText);
        int centerTextX = centerX - coreTextWidth / 2;

        if (!leftIndicator.isEmpty()) {
            int leftTextX = centerTextX - minecraft.font.width(leftIndicator);
            guiGraphics.drawString(minecraft.font, leftIndicator, leftTextX, yPos + barHeight + 5, 0xFFFF0000);
        }

        guiGraphics.drawString(minecraft.font, coreText, centerTextX, yPos + barHeight + 5, 0xFFFFFFFF);

        if (!rightIndicator.isEmpty()) {
            int rightTextX = centerTextX + coreTextWidth;
            guiGraphics.drawString(minecraft.font, rightIndicator, rightTextX, yPos + barHeight + 5, 0xFF0000FF);
        }

        if (uiInfo.reinforcementTime > 0) {
            int minutes = uiInfo.reinforcementTime / 60;
            int seconds = uiInfo.reinforcementTime % 60;
            String reinforcementText = String.format("增援: %d:%02d", minutes, seconds);

            int reinforcementColor = 0xFFFFFFFF;
            if (uiInfo.reinforcingTeam == TeamSystem.Team.RED) {
                reinforcementColor = 0xFFFF0000;
            } else if (uiInfo.reinforcingTeam == TeamSystem.Team.BLUE) {
                reinforcementColor = 0xFF0000FF;
            }

            int reinforcementTextWidth = minecraft.font.width(reinforcementText);
            int reinforcementTextX = centerX - reinforcementTextWidth / 2;
            int reinforcementTextY = yPos + barHeight + 20;

            guiGraphics.drawString(minecraft.font, reinforcementText, reinforcementTextX, reinforcementTextY, reinforcementColor);
        }
    }

    private static String generateForceIndicator(int count, boolean isRed) {
        if (count == 0) return "";

        int indicatorCount = (int) Math.ceil(count / 5.0);
        if (indicatorCount == 0) return "";

        String symbol = isRed ? ">" : "<";
        return symbol.repeat(Math.min(indicatorCount, 10));
    }

    public static void addKnownCaptureBlock(BlockPos pos) {
        knownCaptureBlocks.add(pos);
    }

    public static void removeKnownCaptureBlock(BlockPos pos) {
        knownCaptureBlocks.remove(pos);
        activeCaptureUIs.remove(pos);
    }
}