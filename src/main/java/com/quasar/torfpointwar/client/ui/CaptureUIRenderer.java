package com.quasar.torfpointwar.client.ui;

import com.quasar.torfpointwar.auth.AuthValidator;
import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.*;

public class CaptureUIRenderer {
    
    private static final Minecraft minecraft = Minecraft.getInstance();
    private static final Map<BlockPos, CaptureUIInfo> activeCaptureUIs = new HashMap<>();
    private static final Set<BlockPos> knownCaptureBlocks = new HashSet<>();
    private static int updateCounter = 0;
    private static final int UPDATE_INTERVAL = 10; // 减少更新频率，每10帧更新一次UI，提高性能
    
    // 移除客户端状态管理，直接使用服务器端实时数据
    
    public static class CaptureUIInfo {
        public final CaptureCoreBlockEntity blockEntity;
        public final double distance;
        public final String coreName;
        public final double progress; // 0-100% (0%=完全蓝方, 50%=中立, 100%=完全红方)
        public final TeamSystem.Team controllingTeam;
        public final TeamSystem.Team dominantTeam; // 当前主导方（用于进度条颜色）
        public final int redCount;
        public final int blueCount;
        public final int reinforcementTime; // 增援剩余时间（秒）
        public final TeamSystem.Team reinforcingTeam; // 发动增援的队伍
        
        public CaptureUIInfo(CaptureCoreBlockEntity blockEntity, double distance, String coreName, 
                           double progress, TeamSystem.Team controllingTeam, TeamSystem.Team dominantTeam,
                           int redCount, int blueCount, int reinforcementTime, TeamSystem.Team reinforcingTeam) {
            this.blockEntity = blockEntity;
            this.distance = distance;
            this.coreName = coreName;
            this.progress = progress;
            this.controllingTeam = controllingTeam;
            this.dominantTeam = dominantTeam;
            this.redCount = redCount;
            this.blueCount = blueCount;
            this.reinforcementTime = reinforcementTime;
            this.reinforcingTeam = reinforcingTeam;
        }
    }
    
    @SubscribeEvent
    public static void onRenderGuiOverlay(RenderGuiOverlayEvent.Post event) {
        if (minecraft.player == null || minecraft.level == null) return;
        
        // 客户端UI渲染不进行授权检查，因为：
        // 1. 客户端无法访问服务器计分板
        // 2. 服务器端已经有完整的授权保护
        // 3. UI只是显示，不会造成安全问题
        
        // 限制更新频率，减少性能消耗
        updateCounter++;
        if (updateCounter >= UPDATE_INTERVAL) {
            updateCounter = 0;
            updateCaptureUIs();
        }
        
        // 每帧更新UI信息，但不需要特殊的进度状态管理
        
        renderCaptureUIs(event.getGuiGraphics());
    }
    
    private static void updateCaptureUIs() {
        Map<BlockPos, CaptureUIInfo> newUIs = new HashMap<>();
        
        Player player = minecraft.player;
        Level level = minecraft.level;
        if (player == null || level == null) return;
        
        BlockPos playerPos = player.blockPosition();
        
        // 首先更新已知据点列表，大幅降低更新频率以提高性能
        if (updateCounter % 200 == 0) { // 改为每200帧（约7秒）更新一次，比原来慢3倍多
            updateKnownCaptureBlocks(level, playerPos);
        }
        
        // 只检查已知的据点位置，而不是扫描整个区域
        for (BlockPos checkPos : knownCaptureBlocks) {
            // 距离预检查，但使用更大的范围支持大半径据点
            double distanceSq = player.distanceToSqr(checkPos.getX() + 0.5, checkPos.getY() + 0.5, checkPos.getZ() + 0.5);
            if (distanceSq > 250000) continue; // 扩大到500格距离 (500^2 = 250000)
            
            BlockEntity blockEntity = level.getBlockEntity(checkPos);
            if (blockEntity instanceof CaptureCoreBlockEntity captureCore) {
                double radius = captureCore.getRadius();
                
                // 检查玩家是否在争夺区域内
                if (distanceSq <= radius * radius) {
                    // 使用更高效的实体计数方法
                    EntityCount entityCount = countEntitiesEfficiently(level, checkPos, radius);
                    
                    // 直接使用服务器端的实时进度数据，不进行客户端状态管理
                    double currentProgress = captureCore.getUIProgress();
                    
                    int reinforcementTime = captureCore.getRemainingReinforcementTime();
                    // 基于方块状态判断增援队伍
                    TeamSystem.Team reinforcingTeam = TeamSystem.Team.NEUTRAL;
                    if (reinforcementTime > 0) {
                        // 获取方块状态来判断哪个队伍在使用增援
                        net.minecraft.world.level.block.state.BlockState blockState = level.getBlockState(checkPos);
                        com.quasar.torfpointwar.block.CaptureCore.CaptureState captureState = blockState.getValue(com.quasar.torfpointwar.block.CaptureCore.CAPTURE_STATE);
                        
                        if (captureState == com.quasar.torfpointwar.block.CaptureCore.CaptureState.RED_CAPTURED) {
                            reinforcingTeam = TeamSystem.Team.RED;
                        } else if (captureState == com.quasar.torfpointwar.block.CaptureCore.CaptureState.BLUE_CAPTURED) {
                            reinforcingTeam = TeamSystem.Team.BLUE;
                        }
                    }
                    
                    newUIs.put(checkPos, new CaptureUIInfo(
                        captureCore, 
                        Math.sqrt(distanceSq),
                        captureCore.getCoreName(),
                        currentProgress, // 直接使用服务器端进度
                        captureCore.getControllingTeam(),
                        captureCore.getDominantTeam(),
                        entityCount.redCount,
                        entityCount.blueCount,
                        reinforcementTime,
                        reinforcingTeam
                    ));
                }
            }
        }
        
        activeCaptureUIs.clear();
        activeCaptureUIs.putAll(newUIs);
    }
    
    private static void updateKnownCaptureBlocks(Level level, BlockPos playerPos) {
        // 不清空已知据点，累积发现，提高效率
        // knownCaptureBlocks.clear(); // 注释掉清空操作
        
        // 性能优化：使用分层扫描策略而不是暴力扫描大范围
        
        // 第一层：快速扫描玩家周围小范围（保持原有性能）
        int smallRange = 15; // 小范围快速扫描
        for (int x = -smallRange; x <= smallRange; x++) {
            for (int y = -8; y <= 8; y++) {
                for (int z = -smallRange; z <= smallRange; z++) {
                    BlockPos checkPos = playerPos.offset(x, y, z);
                    BlockEntity blockEntity = level.getBlockEntity(checkPos);
                    if (blockEntity instanceof CaptureCoreBlockEntity) {
                        knownCaptureBlocks.add(checkPos);
                    }
                }
            }
        }
        
        // 第二层：稀疏扫描更大范围（每隔几格检查一次，大幅减少计算量）
        int largeRange = 80;
        int step = 8; // 每隔8格检查一次，减少99%的计算量
        for (int x = -largeRange; x <= largeRange; x += step) {
            for (int y = -10; y <= 10; y += 4) {
                for (int z = -largeRange; z <= largeRange; z += step) {
                    // 跳过已经在小范围内检查过的区域
                    if (Math.abs(x) <= smallRange && Math.abs(z) <= smallRange) continue;
                    
                    BlockPos checkPos = playerPos.offset(x, y, z);
                    BlockEntity blockEntity = level.getBlockEntity(checkPos);
                    if (blockEntity instanceof CaptureCoreBlockEntity) {
                        knownCaptureBlocks.add(checkPos);
                        
                        // 发现据点后，精确扫描其周围小区域
                        for (int dx = -2; dx <= 2; dx++) {
                            for (int dy = -2; dy <= 2; dy++) {
                                for (int dz = -2; dz <= 2; dz++) {
                                    BlockPos nearbyPos = checkPos.offset(dx, dy, dz);
                                    BlockEntity nearbyEntity = level.getBlockEntity(nearbyPos);
                                    if (nearbyEntity instanceof CaptureCoreBlockEntity) {
                                        knownCaptureBlocks.add(nearbyPos);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 定期清理距离太远的据点，避免内存泄漏
        knownCaptureBlocks.removeIf(pos -> {
            double distSq = playerPos.distSqr(pos);
            return distSq > 250000; // 超过500格的据点移除
        });
    }
    
    private static class EntityCount {
        final int redCount;
        final int blueCount;
        
        EntityCount(int redCount, int blueCount) {
            this.redCount = redCount;
            this.blueCount = blueCount;
        }
    }
    
    private static EntityCount countEntitiesEfficiently(Level level, BlockPos center, double radius) {
        // 使用更小的搜索范围，只计算玩家和少数关键实体
        AABB captureArea = new AABB(
            center.getX() - radius, center.getY() - radius, center.getZ() - radius,
            center.getX() + radius + 1, center.getY() + radius + 1, center.getZ() + radius + 1
        );
        
        List<Entity> entitiesInRange = level.getEntitiesOfClass(Entity.class, captureArea);
        int redCount = 0;
        int blueCount = 0;
        
        // 限制检查的实体数量，避免大量实体时的性能问题
        int checkedEntities = 0;
        for (Entity entity : entitiesInRange) {
            if (checkedEntities++ > 30) break; // 降低到最多检查30个实体，提高性能
            
            // 精确距离检查（与服务器端保持一致）
            double entityDistance = entity.distanceToSqr(center.getX() + 0.5, center.getY() + 0.5, center.getZ() + 0.5);
            if (entityDistance > radius * radius) continue;
            
            TeamSystem.Team team = TeamSystem.getEntityTeam(entity);
            if (team == TeamSystem.Team.RED) {
                redCount++;
            } else if (team == TeamSystem.Team.BLUE) {
                blueCount++;
            }
        }
        
        return new EntityCount(redCount, blueCount);
    }
    
    private static void renderCaptureUIs(GuiGraphics guiGraphics) {
        if (activeCaptureUIs.isEmpty()) return;
        
        // 按距离排序，只显示最近的两个
        List<CaptureUIInfo> sortedUIs = activeCaptureUIs.values().stream()
            .sorted(Comparator.comparingDouble(ui -> ui.distance))
            .limit(2)
            .toList();
        
        int screenWidth = minecraft.getWindow().getGuiScaledWidth();
        int yOffset = 10;
        
        for (int i = 0; i < sortedUIs.size(); i++) {
            CaptureUIInfo uiInfo = sortedUIs.get(i);
            // 调整间距以适应增援倒计时显示
            int spacing = uiInfo.reinforcementTime > 0 ? 50 : 35; // 有倒计时时增加间距
            renderSingleCaptureUI(guiGraphics, uiInfo, screenWidth, yOffset + i * spacing);
        }
    }
    
    private static void renderSingleCaptureUI(GuiGraphics guiGraphics, CaptureUIInfo uiInfo, int screenWidth, int yPos) {
        int barWidth = 200; // 保持宽度不变
        int barHeight = 6; // 缩短到原来的1/2
        int centerX = screenWidth / 2;
        int barX = centerX - barWidth / 2;
        
        // 绘制进度条外层黑色边框（更粗）
        guiGraphics.fill(barX - 3, yPos - 3, barX + barWidth + 3, yPos + barHeight + 3, 0xFF000000);
        
        // 绘制进度条内层边框（深灰色）
        guiGraphics.fill(barX - 2, yPos - 2, barX + barWidth + 2, yPos + barHeight + 2, 0xFF404040);
        
        // 绘制进度条背景（半透明灰色，更精致）
        guiGraphics.fill(barX, yPos, barX + barWidth, yPos + barHeight, 0x80202020);
        
        // 战地式双向进度条渲染
        // 0% = 完全蓝方控制，50% = 中立，100% = 完全红方控制
        double progress = uiInfo.progress; // 0到100
        int centerX_bar = barX + barWidth / 2; // 进度条中心位置
        
        int fillColor;
        int fillStart, fillEnd;
        
        if (progress < 50) {
            // 蓝方区域 (0% - 50%)
            fillColor = 0xFF0000FF; // 蓝色
            double blueRatio = (50 - progress) / 50.0; // 蓝方控制程度
            int blueWidth = (int) (barWidth / 2 * blueRatio);
            fillStart = barX;
            fillEnd = barX + blueWidth;
        } else if (progress > 50) {
            // 红方区域 (50% - 100%)
            fillColor = 0xFFFF0000; // 红色
            double redRatio = (progress - 50) / 50.0; // 红方控制程度
            int redWidth = (int) (barWidth / 2 * redRatio);
            fillStart = centerX_bar;
            fillEnd = centerX_bar + redWidth;
        } else {
            // 完全中立 (50%)
            fillStart = fillEnd = centerX_bar;
            fillColor = 0x80808080;
        }
        
        // 绘制中心分界线（白色）
        guiGraphics.fill(centerX_bar - 1, yPos, centerX_bar + 1, yPos + barHeight, 0xFFFFFFFF);
        
        // 绘制进度条填充
        if (fillEnd > fillStart) {
            guiGraphics.fill(fillStart, yPos, fillEnd, yPos + barHeight, fillColor);
        }
        
        // 绘制兵力指示符号（带颜色）
        String leftIndicator = generateForceIndicator(uiInfo.redCount, true);
        String rightIndicator = generateForceIndicator(uiInfo.blueCount, false);
        
        // 计算文本位置
        String coreText = " 【" + uiInfo.coreName + "】 争夺中 ";
        int coreTextWidth = minecraft.font.width(coreText);
        int centerTextX = centerX - coreTextWidth / 2;
        
        // 绘制左侧红色兵力指示
        if (!leftIndicator.isEmpty()) {
            int leftTextX = centerTextX - minecraft.font.width(leftIndicator);
            guiGraphics.drawString(minecraft.font, leftIndicator, leftTextX, yPos + barHeight + 5, 0xFFFF0000); // 红色
        }
        
        // 绘制中心文本（白色）
        guiGraphics.drawString(minecraft.font, coreText, centerTextX, yPos + barHeight + 5, 0xFFFFFFFF);
        
        // 绘制右侧蓝色兵力指示
        if (!rightIndicator.isEmpty()) {
            int rightTextX = centerTextX + coreTextWidth;
            guiGraphics.drawString(minecraft.font, rightIndicator, rightTextX, yPos + barHeight + 5, 0xFF0000FF); // 蓝色
        }
        
        // 绘制增援倒计时（如果存在）
        if (uiInfo.reinforcementTime > 0) {
            int minutes = uiInfo.reinforcementTime / 60;
            int seconds = uiInfo.reinforcementTime % 60;
            String reinforcementText = String.format("增援: %d:%02d", minutes, seconds);
            
            // 获取增援队伍的颜色
            int reinforcementColor = 0xFFFFFFFF; // 默认白色
            if (uiInfo.reinforcingTeam == TeamSystem.Team.RED) {
                reinforcementColor = 0xFFFF0000; // 红色
            } else if (uiInfo.reinforcingTeam == TeamSystem.Team.BLUE) {
                reinforcementColor = 0xFF0000FF; // 蓝色
            }
            
            // 计算增援文本位置（居中显示在进度条下方第二行）
            int reinforcementTextWidth = minecraft.font.width(reinforcementText);
            int reinforcementTextX = centerX - reinforcementTextWidth / 2;
            int reinforcementTextY = yPos + barHeight + 20; // 在兵力指示下方一行
            
            guiGraphics.drawString(minecraft.font, reinforcementText, reinforcementTextX, reinforcementTextY, reinforcementColor);
        }
    }
    
    private static String generateForceIndicator(int count, boolean isRed) {
        if (count == 0) return "";
        
        // 向上取整：1个单位显示1个符号，6个单位显示2个符号
        int indicatorCount = (int) Math.ceil(count / 5.0);
        if (indicatorCount == 0) return "";
        
        String symbol = isRed ? ">" : "<";
        return symbol.repeat(Math.min(indicatorCount, 10)); // 最多显示10个符号
    }
    
    // 公共方法用于从服务器端添加已知据点
    public static void addKnownCaptureBlock(BlockPos pos) {
        knownCaptureBlocks.add(pos);
    }
    
    public static void removeKnownCaptureBlock(BlockPos pos) {
        knownCaptureBlocks.remove(pos);
        activeCaptureUIs.remove(pos);
    }
}