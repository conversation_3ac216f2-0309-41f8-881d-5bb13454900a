package com.quasar.torfpointwar.registry;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.item.CaptureCoreBlockItem;
import com.quasar.torfpointwar.item.PreCapturedCoreBlockItem;
import com.quasar.torfpointwar.item.ReinforcementBanner;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.CreativeModeTabs;
import net.minecraft.world.item.Item;
import net.minecraftforge.event.BuildCreativeModeTabContentsEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

@Mod.EventBusSubscriber(modid = TorfPointWar.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class ModItems {
    public static final DeferredRegister<Item> ITEMS =
        DeferredRegister.create(ForgeRegistries.ITEMS, TorfPointWar.MODID);

    public static final RegistryObject<Item> CAPTURE_CORE = ITEMS.register("capture_core",
        () -> new CaptureCoreBlockItem(ModBlocks.CAPTURE_CORE.get(), new Item.Properties()));

    public static final RegistryObject<Item> RED_CAPTURED_CORE = ITEMS.register("red_captured_core",
        () -> new PreCapturedCoreBlockItem(ModBlocks.CAPTURE_CORE.get(), new Item.Properties(), TeamSystem.Team.RED));

    public static final RegistryObject<Item> BLUE_CAPTURED_CORE = ITEMS.register("blue_captured_core",
        () -> new PreCapturedCoreBlockItem(ModBlocks.CAPTURE_CORE.get(), new Item.Properties(), TeamSystem.Team.BLUE));

    public static final RegistryObject<Item> NEUTRAL_REINFORCEMENT_BANNER = ITEMS.register("neutral_reinforcement_banner",
            () -> new ReinforcementBanner(new Item.Properties().stacksTo(16), ReinforcementBanner.BannerType.NEUTRAL));

    public static final RegistryObject<Item> RED_REINFORCEMENT_BANNER = ITEMS.register("red_reinforcement_banner",
            () -> new ReinforcementBanner(new Item.Properties().stacksTo(16), ReinforcementBanner.BannerType.RED));

    public static final RegistryObject<Item> BLUE_REINFORCEMENT_BANNER = ITEMS.register("blue_reinforcement_banner",
            () -> new ReinforcementBanner(new Item.Properties().stacksTo(16), ReinforcementBanner.BannerType.BLUE));

    public static void register(IEventBus eventBus) {
        ITEMS.register(eventBus);
    }

    @SubscribeEvent
    public static void buildContents(BuildCreativeModeTabContentsEvent event) {
        if (event.getTabKey() == CreativeModeTabs.FUNCTIONAL_BLOCKS) {
            event.accept(CAPTURE_CORE);
            event.accept(RED_CAPTURED_CORE);
            event.accept(BLUE_CAPTURED_CORE);
        }
        if (event.getTabKey() == CreativeModeTabs.TOOLS_AND_UTILITIES) {
            event.accept(NEUTRAL_REINFORCEMENT_BANNER);
            event.accept(RED_REINFORCEMENT_BANNER);
            event.accept(BLUE_REINFORCEMENT_BANNER);
        }
    }
}