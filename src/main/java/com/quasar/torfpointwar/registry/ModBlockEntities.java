package com.quasar.torfpointwar.registry;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.block.entity.CaptureCoreBlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class ModBlockEntities {
    public static final DeferredRegister<BlockEntityType<?>> BLOCK_ENTITIES =
        DeferredRegister.create(ForgeRegistries.BLOCK_ENTITY_TYPES, TorfPointWar.MODID);

    public static final RegistryObject<BlockEntityType<CaptureCoreBlockEntity>> CAPTURE_CORE =
        BLOCK_ENTITIES.register("capture_core", () ->
            BlockEntityType.Builder.of(CaptureCoreBlockEntity::new, ModBlocks.CAPTURE_CORE.get()).build(null));

    public static void register(IEventBus eventBus) {
        BLOCK_ENTITIES.register(eventBus);
    }
}