package com.quasar.torfpointwar.event;

import com.quasar.torfpointwar.auth.AuthValidator;
import com.quasar.torfpointwar.item.ReinforcementBanner;
import com.quasar.torfpointwar.registry.ModItems;
import com.quasar.torfpointwar.team.TeamSystem;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = "torfpointwar")
public class BannerTransformHandler {

    private static int tickCounter = 0;

    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {

        if (event.side.isClient()) {
            return;
        }

        if (!AuthValidator.checkQuietly(event.player.level())) {
            return;
        }

        if (event.phase != TickEvent.Phase.END) {
            return;
        }

        tickCounter++;
        if (tickCounter % 20 != 0) {
            return;
        }

        Player player = event.player;
        ItemStack heldItem = player.getMainHandItem();

        if (heldItem.getItem() == ModItems.NEUTRAL_REINFORCEMENT_BANNER.get()) {
            TeamSystem.Team playerTeam = TeamSystem.getEntityTeam(player);

            if (playerTeam == TeamSystem.Team.NEUTRAL) {
                return;
            }

            ItemStack newBanner;
            String teamName;
            if (playerTeam == TeamSystem.Team.RED) {
                newBanner = new ItemStack(ModItems.RED_REINFORCEMENT_BANNER.get(), heldItem.getCount());
                teamName = "红队";
            } else if (playerTeam == TeamSystem.Team.BLUE) {
                newBanner = new ItemStack(ModItems.BLUE_REINFORCEMENT_BANNER.get(), heldItem.getCount());
                teamName = "蓝队";
            } else {
                return;
            }

            if (heldItem.hasTag()) {
                newBanner.setTag(heldItem.getTag().copy());
            }

            player.setItemInHand(net.minecraft.world.InteractionHand.MAIN_HAND, newBanner);

            player.sendSystemMessage(net.minecraft.network.chat.Component.literal(
                "中立增援旗帜已转换为" + teamName + "增援旗帜！"
            ).withStyle(net.minecraft.ChatFormatting.GREEN));
        }

        ItemStack offhandItem = player.getOffhandItem();
        if (offhandItem.getItem() == ModItems.NEUTRAL_REINFORCEMENT_BANNER.get()) {
            TeamSystem.Team playerTeam = TeamSystem.getEntityTeam(player);

            if (playerTeam == TeamSystem.Team.NEUTRAL) {
                return;
            }

            ItemStack newBanner;
            String teamName;
            if (playerTeam == TeamSystem.Team.RED) {
                newBanner = new ItemStack(ModItems.RED_REINFORCEMENT_BANNER.get(), offhandItem.getCount());
                teamName = "红队";
            } else if (playerTeam == TeamSystem.Team.BLUE) {
                newBanner = new ItemStack(ModItems.BLUE_REINFORCEMENT_BANNER.get(), offhandItem.getCount());
                teamName = "蓝队";
            } else {
                return;
            }

            if (offhandItem.hasTag()) {
                newBanner.setTag(offhandItem.getTag().copy());
            }

            player.setItemInHand(net.minecraft.world.InteractionHand.OFF_HAND, newBanner);

            player.sendSystemMessage(net.minecraft.network.chat.Component.literal(
                "中立增援旗帜已转换为" + teamName + "增援旗帜！"
            ).withStyle(net.minecraft.ChatFormatting.GREEN));
        }
    }
}