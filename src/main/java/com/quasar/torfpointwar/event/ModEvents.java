package com.quasar.torfpointwar.event;

import com.quasar.torfpointwar.TorfPointWar;
import com.quasar.torfpointwar.command.TorfPointWarCommands;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = TorfPointWar.MODID)
public class ModEvents {
    
    @SubscribeEvent
    public static void onRegisterCommands(RegisterCommandsEvent event) {
        TorfPointWarCommands.register(event.getDispatcher());
    }
}