{"permissions": {"allow": ["<PERSON><PERSON>(./gradlew:*)", "Bash(JAVA_HOME=\"/c/Program Files/Eclipse Adoptium/jdk-*********-hotspot\" PATH=\"/c/Program Files/Eclipse Adoptium/jdk-*********-hotspot/bin:$PATH\" ./gradlew build)", "Bash(java:*)", "Bash(set JAVA_HOME=C:Program FilesEclipse Adoptiumjdk-**********-hotspot)", "Bash(set PATH=%JAVA_HOME%bin)", "Bash(%PATH%)", "Bash(./build.bat)", "Bash(cmd.exe:*)", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(del create_banner_textures.py)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(.gradlew.bat runClient)", "<PERSON><PERSON>(gradlew.bat runClient:*)"], "deny": []}}